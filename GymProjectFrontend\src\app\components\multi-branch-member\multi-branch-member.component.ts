import { Component, OnInit, OnDestroy } from '@angular/core';
import { MultiBranchMember, MemberDeleteOptions } from '../../models/multi-branch-member.model';
import { MemberService } from '../../services/member.service';
import { MembershipService } from '../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { faEdit, faTrashAlt, faInfoCircle, faSyncAlt, faUserPlus, faComment, faDumbbell } from '@fortawesome/free-solid-svg-icons';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-multi-branch-member',
  templateUrl: './multi-branch-member.component.html',
  styleUrls: ['./multi-branch-member.component.css'],
  standalone: false
})
export class MultiBranchMemberComponent implements OnInit, OnDestroy {
  members: MultiBranchMember[] = [];
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  searchText = '';
  isLoading = false;
  selectedGender: number | null = null;
  selectedBranch: string = '';
  
  // Available branches for filtering
  availableBranches: string[] = ['Fitness', 'Crossfit', 'Pilates', 'Yoga', 'Boxing'];

  // Icons
  faTrashAlt = faTrashAlt;
  faEdit = faEdit;
  faInfoCircle = faInfoCircle;
  faSyncAlt = faSyncAlt;
  faUserPlus = faUserPlus;
  faComment = faComment;
  faDumbbell = faDumbbell;

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(
    private memberService: MemberService,
    private membershipService: MembershipService,
    private toastrService: ToastrService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadMembers();
    
    // Search debouncing
    this.searchSubject.pipe(
      debounceTime(300),
      takeUntil(this.destroy$)
    ).subscribe(searchText => {
      this.searchText = searchText;
      this.currentPage = 1;
      this.loadMembers();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadMembers(): void {
    this.isLoading = true;
    
    this.memberService.getMultiBranchMembers(
      this.currentPage, 
      this.searchText, 
      this.selectedGender,
      this.selectedBranch
    ).subscribe({
      next: (response) => {
        if (response.success) {
          this.members = response.data.data;
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching multi-branch members:', error);
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  onSearch(event: any): void {
    this.searchSubject.next(event.target.value);
  }

  onGenderFilterChange(): void {
    this.currentPage = 1;
    this.loadMembers();
  }

  onBranchFilterChange(): void {
    this.currentPage = 1;
    this.loadMembers();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadMembers();
  }

  refreshMembers(): void {
    this.currentPage = 1;
    this.loadMembers();
  }

  clearFilters(): void {
    this.searchText = '';
    this.selectedGender = null;
    this.selectedBranch = '';
    this.currentPage = 1;
    this.loadMembers();
  }

  onDeleteMember(member: MultiBranchMember): void {
    // Çoklu üyelik kontrolü
    if (member.memberships.length > 1) {
      this.showDeleteOptions(member);
    } else {
      this.confirmSingleMembershipDelete(member);
    }
  }

  private showDeleteOptions(member: MultiBranchMember): void {
    this.memberService.getMemberDeleteOptions(member.memberID).subscribe({
      next: (response) => {
        if (response.success) {
          // Delete options dialog açılacak (ayrı component olarak)
          this.openDeleteOptionsDialog(response.data);
        }
      },
      error: (error) => {
        this.toastrService.error('Silme seçenekleri alınırken hata oluştu', 'Hata');
      }
    });
  }

  private confirmSingleMembershipDelete(member: MultiBranchMember): void {
    if (confirm(`${member.name} adlı üyeyi silmek istediğinizden emin misiniz?`)) {
      const membershipId = member.memberships[0].membershipID;
      this.membershipService.deleteSpecificMembership(membershipId, member.memberID).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Üye başarıyla silindi');
            this.loadMembers();
          }
        },
        error: (error) => {
          this.toastrService.error('Üye silinirken bir hata oluştu');
        }
      });
    }
  }

  private openDeleteOptionsDialog(deleteOptions: MemberDeleteOptions): void {
    // Bu dialog component'i ayrı olarak oluşturulacak
    console.log('Delete options:', deleteOptions);
    // Şimdilik console.log ile gösterelim
    this.toastrService.info(`${deleteOptions.memberName} için ${deleteOptions.activeMemberships.length} aktif üyelik bulundu`);
  }

  getBranchBadgeClass(branch: string): string {
    const branchClasses: { [key: string]: string } = {
      'Fitness': 'badge-fitness',
      'Crossfit': 'badge-crossfit', 
      'Pilates': 'badge-pilates',
      'Yoga': 'badge-yoga',
      'Boxing': 'badge-boxing'
    };
    return branchClasses[branch] || 'badge-default';
  }

  getGenderIcon(gender: number): string {
    return gender === 1 ? 'fas fa-mars' : 'fas fa-venus';
  }

  getGenderColor(gender: number): string {
    return gender === 1 ? '#007bff' : '#e91e63';
  }

  formatRemainingDays(days: number): string {
    if (days <= 0) return 'Süresi Dolmuş';
    if (days === 1) return '1 Gün';
    return `${days} Gün`;
  }

  getRemainingDaysClass(days: number): string {
    if (days <= 0) return 'text-danger';
    if (days <= 7) return 'text-warning';
    return 'text-success';
  }
}
