export interface MultiBranchMember {
  memberID: number;
  name: string;
  gender: number;
  phoneNumber: string;
  email: string;
  balance: number;
  isActive?: boolean;
  creationDate?: Date;
  memberships: MembershipBranch[];
  combinedBranches: string;
  totalRemainingDays: number;
  earliestEndDate?: Date;
  latestEndDate?: Date;
}

export interface MembershipBranch {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  isActive: boolean;
  isFrozen: boolean;
  freezeStartDate?: Date;
  freezeEndDate?: Date;
}

export interface MembershipDeleteOption {
  membershipID: number;
  memberID: number;
  memberName: string;
  branch: string;
  typeName: string;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  totalPaidAmount: number;
  paymentCount: number;
  hasActivePayments: boolean;
  isActive: boolean;
}

export interface MemberDeleteOptions {
  memberID: number;
  memberName: string;
  activeMemberships: MembershipDeleteOption[];
  hasMultipleMemberships: boolean;
}

export interface MembershipAddRequest {
  memberID: number;
  membershipTypeID: number;
  startDate: Date;
  endDate: Date;
  paymentStatus: string;
  price: number;
  paymentMethod: string;
  day: number;
  isRenewal: boolean;
  existingMembershipID?: number;
}
