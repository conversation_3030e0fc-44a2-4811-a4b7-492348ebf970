-- Çoklu Branş Üyelik Sistemi Migration
-- Bu script çoklu branş üyelik sistemini desteklemek için gerekli optimizasyonları yapar
-- Performans indexleri ve constraint'ler eklenir

USE [GymProject]
GO

PRINT 'Çoklu Branş Üyelik Sistemi Migration başlatılıyor...'
GO

-- 1. Performans İndeksleri Ekleme
-- Üyelik sorgularını hızlandırmak için composite indexler

-- Üye ID ve Üyelik Türü bazlı sorgular için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Memberships_MemberID_MembershipTypeID_Active')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Memberships_MemberID_MembershipTypeID_Active] 
    ON [dbo].[Memberships] ([MemberID], [MembershipTypeID], [IsActive])
    INCLUDE ([StartDate], [EndDate], [CreationDate], [UpdatedDate])
    GO
    PRINT 'Index IX_Memberships_MemberID_MembershipTypeID_Active oluşturuldu.'
END

-- Aktif üyelikler için optimizasyon
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Memberships_Active_EndDate')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Memberships_Active_EndDate] 
    ON [dbo].[Memberships] ([IsActive], [EndDate])
    INCLUDE ([MemberID], [MembershipTypeID], [StartDate])
    GO
    PRINT 'Index IX_Memberships_Active_EndDate oluşturuldu.'
END

-- Şirket bazlı üyelik sorguları için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Memberships_CompanyID_MemberID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Memberships_CompanyID_MemberID] 
    ON [dbo].[Memberships] ([CompanyID], [MemberID])
    INCLUDE ([MembershipTypeID], [IsActive], [EndDate])
    GO
    PRINT 'Index IX_Memberships_CompanyID_MemberID oluşturuldu.'
END

-- Üyelik türü ve branş sorguları için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MembershipTypes_CompanyID_Branch')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MembershipTypes_CompanyID_Branch] 
    ON [dbo].[MembershipTypes] ([CompanyID], [Branch])
    INCLUDE ([TypeName], [Day], [Price], [IsActive])
    GO
    PRINT 'Index IX_MembershipTypes_CompanyID_Branch oluşturuldu.'
END

-- Ödeme sorguları için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Payments_MemberShipID_Active')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Payments_MemberShipID_Active] 
    ON [dbo].[Payments] ([MemberShipID], [IsActive])
    INCLUDE ([PaymentAmount], [PaymentDate], [PaymentStatus])
    GO
    PRINT 'Index IX_Payments_MemberShipID_Active oluşturuldu.'
END

-- Üye sorguları için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Members_CompanyID_Active')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Members_CompanyID_Active] 
    ON [dbo].[Members] ([CompanyID], [IsActive])
    INCLUDE ([Name], [PhoneNumber], [Gender], [Email])
    GO
    PRINT 'Index IX_Members_CompanyID_Active oluşturuldu.'
END

-- 2. Çoklu Branş Üyelik Görünümü (View) Oluşturma
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_MultiBranchMembers')
BEGIN
    DROP VIEW [dbo].[vw_MultiBranchMembers]
    PRINT 'Mevcut vw_MultiBranchMembers view silindi.'
END
GO

CREATE VIEW [dbo].[vw_MultiBranchMembers]
AS
SELECT 
    m.MemberID,
    m.CompanyID,
    m.Name,
    m.Gender,
    m.PhoneNumber,
    m.Email,
    m.Balance,
    m.IsActive,
    m.CreationDate,
    -- Aktif üyeliklerin birleşik gösterimi
    STUFF((
        SELECT ', ' + mt.Branch + '(' + CAST(
            CASE 
                WHEN ms.EndDate >= GETDATE() 
                THEN CEILING(DATEDIFF(DAY, GETDATE(), ms.EndDate))
                ELSE 0 
            END AS VARCHAR(10)
        ) + ')'
        FROM Memberships ms
        INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
        WHERE ms.MemberID = m.MemberID 
            AND ms.IsActive = 1 
            AND ms.EndDate >= GETDATE()
            AND mt.CompanyID = m.CompanyID
        ORDER BY ms.EndDate DESC
        FOR XML PATH('')
    ), 1, 2, '') AS CombinedBranches,
    
    -- Toplam kalan gün sayısı
    ISNULL((
        SELECT SUM(
            CASE 
                WHEN ms.EndDate >= GETDATE() 
                THEN CEILING(DATEDIFF(DAY, GETDATE(), ms.EndDate))
                ELSE 0 
            END
        )
        FROM Memberships ms
        WHERE ms.MemberID = m.MemberID 
            AND ms.IsActive = 1 
            AND ms.EndDate >= GETDATE()
    ), 0) AS TotalRemainingDays,
    
    -- En erken bitiş tarihi
    (
        SELECT MIN(ms.EndDate)
        FROM Memberships ms
        WHERE ms.MemberID = m.MemberID 
            AND ms.IsActive = 1 
            AND ms.EndDate >= GETDATE()
    ) AS EarliestEndDate,
    
    -- En geç bitiş tarihi
    (
        SELECT MAX(ms.EndDate)
        FROM Memberships ms
        WHERE ms.MemberID = m.MemberID 
            AND ms.IsActive = 1 
            AND ms.EndDate >= GETDATE()
    ) AS LatestEndDate,
    
    -- Aktif üyelik sayısı
    (
        SELECT COUNT(*)
        FROM Memberships ms
        WHERE ms.MemberID = m.MemberID 
            AND ms.IsActive = 1 
            AND ms.EndDate >= GETDATE()
    ) AS ActiveMembershipCount

FROM Members m
WHERE m.IsActive = 1
GO

PRINT 'vw_MultiBranchMembers view oluşturuldu.'
GO

-- 3. Performans İstatistikleri Güncelleme
UPDATE STATISTICS [dbo].[Members]
UPDATE STATISTICS [dbo].[Memberships]
UPDATE STATISTICS [dbo].[MembershipTypes]
UPDATE STATISTICS [dbo].[Payments]
GO

PRINT 'Performans istatistikleri güncellendi.'
GO

-- 4. Constraint Kontrolleri
-- Foreign Key constraint'lerin mevcut olduğunu kontrol et
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Memberships_Members')
BEGIN
    ALTER TABLE [dbo].[Memberships]
    ADD CONSTRAINT [FK_Memberships_Members] 
    FOREIGN KEY([MemberID]) REFERENCES [dbo].[Members] ([MemberID])
    PRINT 'FK_Memberships_Members constraint eklendi.'
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Memberships_MembershipTypes')
BEGIN
    ALTER TABLE [dbo].[Memberships]
    ADD CONSTRAINT [FK_Memberships_MembershipTypes] 
    FOREIGN KEY([MembershipTypeID]) REFERENCES [dbo].[MembershipTypes] ([MembershipTypeID])
    PRINT 'FK_Memberships_MembershipTypes constraint eklendi.'
END

-- 5. Temizlik ve Optimizasyon
-- Eski, kullanılmayan kayıtları temizle (opsiyonel)
-- DELETE FROM Memberships WHERE IsActive = 0 AND DeletedDate < DATEADD(YEAR, -2, GETDATE())

PRINT 'Çoklu Branş Üyelik Sistemi Migration tamamlandı!'
PRINT 'Eklenen özellikler:'
PRINT '- Performans indexleri (6 adet)'
PRINT '- vw_MultiBranchMembers view'
PRINT '- Foreign key constraint kontrolleri'
PRINT '- İstatistik güncellemeleri'
PRINT ''
-- 6. Ek Performans Optimizasyonları
-- Çoklu branş sorguları için özel indexler

-- Üye arama performansı için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Members_Search_Performance')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Members_Search_Performance]
    ON [dbo].[Members] ([CompanyID], [IsActive], [Name], [PhoneNumber])
    INCLUDE ([Gender], [Email], [Balance], [CreationDate])
    GO
    PRINT 'Index IX_Members_Search_Performance oluşturuldu.'
END

-- Üyelik bitiş tarihi sorguları için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Memberships_EndDate_Performance')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Memberships_EndDate_Performance]
    ON [dbo].[Memberships] ([CompanyID], [IsActive], [EndDate] DESC)
    INCLUDE ([MemberID], [MembershipTypeID], [StartDate])
    GO
    PRINT 'Index IX_Memberships_EndDate_Performance oluşturuldu.'
END

-- Giriş-çıkış performansı için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_EntryExitHistories_Performance')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_EntryExitHistories_Performance]
    ON [dbo].[EntryExitHistories] ([CompanyID], [IsActive], [EntryDate] DESC)
    INCLUDE ([MembershipID], [ExitDate])
    GO
    PRINT 'Index IX_EntryExitHistories_Performance oluşturuldu.'
END

-- 7. Stored Procedure Optimizasyonları
-- Çoklu branş üye listesi için optimize edilmiş stored procedure
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetMultiBranchMembers')
BEGIN
    DROP PROCEDURE [dbo].[sp_GetMultiBranchMembers]
    PRINT 'Mevcut sp_GetMultiBranchMembers procedure silindi.'
END
GO

CREATE PROCEDURE [dbo].[sp_GetMultiBranchMembers]
    @CompanyID INT,
    @PageNumber INT = 1,
    @PageSize INT = 50,
    @SearchText NVARCHAR(100) = '',
    @Gender INT = NULL,
    @Branch NVARCHAR(50) = ''
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;

    WITH MemberCTE AS (
        SELECT DISTINCT m.MemberID
        FROM Members m
        INNER JOIN Memberships ms ON m.MemberID = ms.MemberID
        INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
        WHERE m.CompanyID = @CompanyID
            AND m.IsActive = 1
            AND ms.IsActive = 1
            AND ms.EndDate >= GETDATE()
            AND (@SearchText = '' OR m.Name LIKE '%' + @SearchText + '%' OR m.PhoneNumber LIKE '%' + @SearchText + '%')
            AND (@Gender IS NULL OR m.Gender = @Gender)
            AND (@Branch = '' OR mt.Branch = @Branch)
    )
    SELECT
        m.MemberID,
        m.Name,
        m.Gender,
        m.PhoneNumber,
        m.Email,
        m.Balance,
        m.IsActive,
        m.CreationDate,
        COUNT(*) OVER() as TotalCount
    FROM Members m
    INNER JOIN MemberCTE cte ON m.MemberID = cte.MemberID
    ORDER BY m.Name
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
GO

PRINT 'sp_GetMultiBranchMembers stored procedure oluşturuldu.'
GO

-- 8. Maintenance Plan Önerileri
PRINT ''
PRINT '📊 PERFORMANS İZLEME ÖNERİLERİ:'
PRINT '1. Index kullanım istatistiklerini düzenli kontrol edin:'
PRINT '   SELECT * FROM sys.dm_db_index_usage_stats WHERE database_id = DB_ID()'
PRINT ''
PRINT '2. Yavaş sorguları izleyin:'
PRINT '   SELECT * FROM sys.dm_exec_query_stats ORDER BY total_elapsed_time DESC'
PRINT ''
PRINT '3. Index fragmentasyonunu kontrol edin:'
PRINT '   SELECT * FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, NULL)'
PRINT ''
PRINT '4. Haftalık maintenance planı:'
PRINT '   - Index reorganize/rebuild'
PRINT '   - İstatistik güncellemeleri'
PRINT '   - Eski kayıt temizliği'

PRINT ''
PRINT '⚠️  ÖNEMLİ: Bu migration 100+ salon için optimize edilmiştir.'
PRINT '⚠️  Büyük veritabanlarında çalıştırmadan önce test ortamında deneyin.'
PRINT '⚠️  Stored procedure kullanımı için backend kodunda gerekli değişiklikler yapılmalıdır.'
GO
