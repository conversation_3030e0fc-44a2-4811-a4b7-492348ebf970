import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MemberDeleteOptions, MembershipDeleteOption } from '../../models/multi-branch-member.model';
import { MembershipService } from '../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { faTrashAlt, faExclamationTriangle, faInfoCircle, faMoneyBillWave } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-member-delete-options-dialog',
  templateUrl: './member-delete-options-dialog.component.html',
  styleUrls: ['./member-delete-options-dialog.component.css'],
  standalone: false
})
export class MemberDeleteOptionsDialogComponent {
  faTrashAlt = faTrashAlt;
  faExclamationTriangle = faExclamationTriangle;
  faInfoCircle = faInfoCircle;
  faMoneyBillWave = faMoneyBillWave;

  selectedMembershipId: number | null = null;
  isDeleting = false;

  constructor(
    public dialogRef: MatDialogRef<MemberDeleteOptionsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MemberDeleteOptions,
    private membershipService: MembershipService,
    private toastrService: ToastrService
  ) {}

  onSelectMembership(membership: MembershipDeleteOption): void {
    this.selectedMembershipId = membership.membershipID;
  }

  onDeleteSelected(): void {
    if (!this.selectedMembershipId) {
      this.toastrService.warning('Lütfen silinecek üyeliği seçin', 'Uyarı');
      return;
    }

    const selectedMembership = this.data.activeMemberships.find(
      m => m.membershipID === this.selectedMembershipId
    );

    if (!selectedMembership) {
      this.toastrService.error('Seçilen üyelik bulunamadı', 'Hata');
      return;
    }

    // Ödeme uyarısı
    if (selectedMembership.hasActivePayments) {
      const confirmMessage = `Bu üyeliğin aktif ödemeleri bulunmaktadır. Silme işlemi devam etsin mi?\n\n` +
        `Branş: ${selectedMembership.branch}\n` +
        `Paket: ${selectedMembership.typeName}\n` +
        `Toplam Ödeme: ${selectedMembership.totalPaidAmount} TL\n` +
        `Ödeme Sayısı: ${selectedMembership.paymentCount}`;

      if (!confirm(confirmMessage)) {
        return;
      }
    }

    this.isDeleting = true;

    this.membershipService.deleteSpecificMembership(
      this.selectedMembershipId,
      this.data.memberID
    ).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success('Seçilen üyelik başarıyla silindi', 'Başarılı');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message || 'Silme işlemi başarısız', 'Hata');
        }
        this.isDeleting = false;
      },
      error: (error) => {
        this.toastrService.error('Üyelik silinirken bir hata oluştu', 'Hata');
        this.isDeleting = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  getBranchBadgeClass(branch: string): string {
    const branchClasses: { [key: string]: string } = {
      'Fitness': 'badge-fitness',
      'Crossfit': 'badge-crossfit',
      'Pilates': 'badge-pilates',
      'Yoga': 'badge-yoga',
      'Boxing': 'badge-boxing'
    };
    return branchClasses[branch] || 'badge-default';
  }

  formatRemainingDays(days: number): string {
    if (days <= 0) return 'Süresi Dolmuş';
    if (days === 1) return '1 Gün';
    return `${days} Gün`;
  }

  getRemainingDaysClass(days: number): string {
    if (days <= 0) return 'text-danger';
    if (days <= 7) return 'text-warning';
    return 'text-success';
  }

  getPaymentStatusClass(hasActivePayments: boolean): string {
    return hasActivePayments ? 'text-warning' : 'text-success';
  }

  getPaymentStatusText(hasActivePayments: boolean): string {
    return hasActivePayments ? 'Bekleyen Ödeme Var' : 'Ödeme Tamamlandı';
  }
}
