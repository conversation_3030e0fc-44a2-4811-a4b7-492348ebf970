using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Entities;
using Core.Entities.Concrete;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace Business.Concrete
{
    public class MemberManager : IMemberService
    {
        private const string Chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
        IMemberDal _memberDal;
        IMembershipDal _membershipDal;
        IEntryExitHistoryService _entryExitHistoryService;
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;
        private readonly IUserService _userService;
        private readonly IUserDeviceService _userDeviceService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IQrCodeEncryptionService _qrCodeEncryptionService;

        public MemberManager(
            IMemberDal memberDal,
            IMembershipDal membershipDal,
            IEntryExitHistoryService entryExitHistoryService,
            Core.Utilities.Security.CompanyContext.ICompanyContext companyContext,
            IUserService userService,
            IUserDeviceService userDeviceService,
            IHttpContextAccessor httpContextAccessor,
            IQrCodeEncryptionService qrCodeEncryptionService)
        {
            _memberDal = memberDal;
            _membershipDal = membershipDal;
            _entryExitHistoryService = entryExitHistoryService;
            _companyContext = companyContext;
            _userService = userService;
            _userDeviceService = userDeviceService;
            _httpContextAccessor = httpContextAccessor;
            _qrCodeEncryptionService = qrCodeEncryptionService;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Member", "BranchCount")]
        public IDataResult<Dictionary<string, int>> GetBranchCounts()
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                var companyId = _companyContext.GetCompanyId();

                var branchCounts = (from m in context.Members
                                    join ms in context.Memberships on m.MemberID equals ms.MemberID
                                    join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                    where ms.IsActive == true &&
                                          ms.EndDate > DateTime.Now &&
                                          ms.IsFrozen == false &&
                                          m.CompanyID == companyId && // Şirket ID'sine göre filtrele
                                          ms.CompanyID == companyId && // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                                          mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                                    select new { m.MemberID, mt.Branch }) // MemberID'yi de seç
                                   .Distinct() // Aynı MemberID ve Branch kombinasyonunu tekrarlama
                                   .GroupBy(x => x.Branch)
                                   .ToDictionary(
                                       g => g.Key,
                                       g => g.Count()
                                   );

                return new SuccessDataResult<Dictionary<string, int>>(branchCounts);
            }
        }


        [SecuredOperation("owner,admin")]
        public IDataResult<List<MemberEntryDto>> GetMemberEntriesByName(string name)
        {
            return new SuccessDataResult<List<MemberEntryDto>>(_memberDal.GetMemberEntriesByName(name));
        }
        [SecuredOperation("owner,admin")]
        public IDataResult<PaginatedResult<Member>> GetAllPaginated(MemberPagingParameters parameters)
        {
            var result = _memberDal.GetAllPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<Member>>(result);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MemberFilter>> GetMemberDetailsPaginated(MemberPagingParameters parameters)
        {
            var result = _memberDal.GetMemberDetailsPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MemberFilter>>(result);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Member", "ActiveCount")]
        public IDataResult<int> GetTotalActiveMembers()
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                var companyId = _companyContext.GetCompanyId();

                var totalActive = context.Members
                    .Join(context.Memberships,
                        m => m.MemberID,
                        ms => ms.MemberID,
                        (m, ms) => new { Member = m, Membership = ms })
                    .Where(x => x.Membership.IsActive == true &&
                               x.Membership.EndDate > DateTime.Now &&
                               x.Membership.IsFrozen == false &&
                               x.Member.CompanyID == companyId && // Şirket ID'sine göre filtrele
                               x.Membership.CompanyID == companyId) // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                    .Select(x => x.Member.MemberID) // Sadece MemberID'leri seç
                    .Distinct() // Tekrarlayan MemberID'leri ele
                    .Count();

                return new SuccessDataResult<int>(totalActive);
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Member", "RegisteredCount")]
        public IDataResult<int> GetTotalRegisteredMembers()
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                var companyId = _companyContext.GetCompanyId();

                var totalRegistered = context.Members
                    .Where(m => m.IsActive == true && m.CompanyID == companyId)
                    .Count();

                return new SuccessDataResult<int>(totalRegistered);
            }
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Member", "GenderCount")]
        public IDataResult<Dictionary<string, int>> GetActiveMemberCounts()
        {
            using (var context = new GymContext())
            {
                var counts = new Dictionary<string, int>();
                var today = DateTime.Now;

                // Mevcut kullanıcının şirket ID'sini al
                var companyId = _companyContext.GetCompanyId();

                var activeMembers = (from m in context.Members
                                     join ms in context.Memberships on m.MemberID equals ms.MemberID
                                     where ms.IsActive == true
                                     && ms.EndDate > today
                                     && ms.IsFrozen == false
                                     && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                     && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                                     select new { m.MemberID, m.Gender }) // MemberID'yi de seç
                                   .Distinct() // Aynı MemberID'ye sahip kayıtları tekrarlama
                                   .ToList();

                counts["male"] = activeMembers.Count(m => m.Gender == 1);
                counts["female"] = activeMembers.Count(m => m.Gender == 2);

                return new SuccessDataResult<Dictionary<string, int>>(counts);
            }
        }


        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberEntryDto>> GetTodayEntries(DateTime date)
        {
            return new SuccessDataResult<List<MemberEntryDto>>(_memberDal.GetTodayEntries(date));
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(MemberValidator))]
        [SmartCacheRemoveAspect("Member")]
        public IResult Add(Member member)
        {
            member.Name = member.Name.ToUpper();
            member.ScanNumber = GenerateUniqueQRCode();

            // E-posta adresi kontrolü
            if (!string.IsNullOrEmpty(member.Email))
            {
                // Kullanıcı sistemde kayıtlı mı kontrol et
                var existingUser = _userService.GetByMail(member.Email);
                if (existingUser != null)
                {
                    // Kullanıcı bulundu, Member ile ilişkilendir
                    member.UserID = existingUser.UserID;
                    _memberDal.Add(member);
                    return new SuccessResult(Messages.MemberLinkedWithUser);
                }
                else
                {
                    // Kullanıcı bulunamadı, otomatik kullanıcı hesabı oluştur
                    if (!string.IsNullOrEmpty(member.PhoneNumber) && member.PhoneNumber.Length >= 4)
                    {
                        // Yeni kullanıcı oluştur
                        string tempPassword = member.PhoneNumber.Substring(member.PhoneNumber.Length - 4);
                        CreateNewUserForMember(member);
                        _memberDal.Add(member);

                        return new SuccessResult("Üye eklendi ve otomatik kullanıcı hesabı oluşturuldu. Geçici şifre: " + tempPassword);
                    }
                }
            }

            _memberDal.Add(member);
            return new SuccessResult(Messages.MemberAdded);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Member")]
        public IResult Delete(int id)
        {
            // Silinecek Member'ı al
            var member = _memberDal.Get(m => m.MemberID == id);
            if (member == null)
            {
                return new ErrorResult("Üye bulunamadı.");
            }

            // Member'ı soft delete yap
            member.IsActive = false;
            member.DeletedDate = DateTime.Now;
            _memberDal.Update(member);

            // Eğer bu Member'ın UserID'si varsa, diğer aktif Member kayıtlarını kontrol et
            if (member.UserID.HasValue)
            {
                // Bu User'ın başka aktif Member kayıtları var mı kontrol et
                var otherActiveMembers = _memberDal.GetAll(m =>
                    m.UserID == member.UserID &&
                    m.MemberID != id &&
                    m.IsActive == true);

                // Eğer başka aktif Member'ı yoksa RequirePasswordChange=true yap
                if (otherActiveMembers.Count == 0)
                {
                    var userResult = _userService.GetById(member.UserID.Value);
                    if (userResult.Success && userResult.Data != null)
                    {
                        var user = userResult.Data;
                        user.RequirePasswordChange = true;
                        user.UpdatedDate = DateTime.Now;
                        _userService.Update(user);
                    }
                }
            }

            return new SuccessResult(Messages.MemberDeleted);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Member", "All")]
        public IDataResult<List<Member>> GetAll()
        {
            return new SuccessDataResult<List<Member>>(_memberDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        public IDataResult<List<MembeFilterDto>> GetMemberDetails()
        {
            return new SuccessDataResult<List<MembeFilterDto>>(_memberDal.GetMemberDetails());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MemberDetailWithHistoryDto> GetMemberDetailById(int memberId)
        {
            var memberDetail = _memberDal.GetMemberDetailById(memberId);
            if (memberDetail == null)
            {
                return new ErrorDataResult<MemberDetailWithHistoryDto>(Messages.MemberNotFound);
            }

            // Aktif ve dondurulmamış üyeliklerden bitiş tarihi en ileri olanı bul
            var activeMembership = memberDetail.Memberships?
                .Where(m => m.IsActive && !m.IsFrozen && m.EndDate > DateTime.Now)
                .OrderByDescending(m => m.EndDate)
                .FirstOrDefault();

            if (activeMembership != null)
            {
                var remainingTime = activeMembership.EndDate - DateTime.Now;
                memberDetail.RemainingDays = (int)Math.Ceiling(remainingTime.TotalDays);
            }
            else
            {
                memberDetail.RemainingDays = null; // Aktif üyelik yoksa null ata
            }

            return new SuccessDataResult<MemberDetailWithHistoryDto>(memberDetail);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "Member", "Birthdays")]
        public IDataResult<List<MemberBirthdayDto>> GetUpcomingBirthdays(int days)
        {
            return new SuccessDataResult<List<MemberBirthdayDto>>(_memberDal.GetUpcomingBirthdays(days));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<GetActiveMemberDto>> GetActiveMembers()
        {
            return new SuccessDataResult<List<GetActiveMemberDto>>(_memberDal.GetActiveMembers());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberEntryExitHistoryDto>> GetMemberEntryExitHistory()
        {
            return new SuccessDataResult<List<MemberEntryExitHistoryDto>>(_memberDal.GetMemberEntryExitHistory());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberRemainingDayDto>> GetMemberRemainingDay()
        {
            return new SuccessDataResult<List<MemberRemainingDayDto>>(_memberDal.GetMemberRemainingDay());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Member")]
        [ValidationAspect(typeof(MemberValidator))]
        public IResult Update(Member member)
        {
            // Mevcut üye bilgilerini al
            var existingMember = _memberDal.Get(m => m.MemberID == member.MemberID);
            if (existingMember == null)
            {
                return new ErrorResult(Messages.MemberNotFound);
            }

            // E-posta adresi kontrolü
            if (!string.IsNullOrEmpty(member.Email))
            {
                // E-posta değişmiş mi kontrol et
                if (existingMember.Email != member.Email)
                {
                    // Üyenin mevcut bir UserID'si var mı kontrol et
                    if (existingMember.UserID.HasValue)
                    {
                        // Mevcut User kaydını al
                        var existingUserResult = _userService.GetById(existingMember.UserID.Value);
                        if (existingUserResult.Success && existingUserResult.Data != null)
                        {
                            // Mevcut User kaydını güncelle
                            var userToUpdate = existingUserResult.Data;
                            userToUpdate.Email = member.Email;

                            // Ad ve soyad güncelleme (boşluğa göre)
                            string firstName = member.Name;
                            string lastName = "";

                            int spaceIndex = member.Name.IndexOf(' ');
                            if (spaceIndex > 0)
                            {
                                firstName = member.Name.Substring(0, spaceIndex);
                                lastName = member.Name.Substring(spaceIndex + 1);
                            }

                            userToUpdate.FirstName = firstName;
                            userToUpdate.LastName = lastName;
                            userToUpdate.UpdatedDate = DateTime.Now;

                            // User kaydını güncelle
                            _userService.Update(userToUpdate);

                            // Member ile User ilişkisini koru
                            member.UserID = existingMember.UserID;
                        }
                        else
                        {
                            // Mevcut User kaydı bulunamadı, yeni bir User kaydı oluştur
                            CreateNewUserForMember(member);
                        }
                    }
                    else
                    {
                        // Kullanıcı sistemde kayıtlı mı kontrol et
                        var existingUser = _userService.GetByMail(member.Email);
                        if (existingUser != null)
                        {
                            // Kullanıcı bulundu, Member ile ilişkilendir
                            member.UserID = existingUser.UserID;
                        }
                        else
                        {
                            // Kullanıcı bulunamadı, otomatik kullanıcı hesabı oluştur
                            CreateNewUserForMember(member);
                        }
                    }
                }
                else
                {
                    // E-posta değişmemiş, mevcut UserID'yi koru
                    member.UserID = existingMember.UserID;
                }
            }
            else
            {
                // E-posta boş ise UserID'yi null yap
                member.UserID = null;
            }

            _memberDal.Update(member);
            return new SuccessResult(Messages.MemberUpdated);
        }
        //SECUREDOPERATION ASPECT KOYMA PUBLIC PING STATION
        [PerformanceAspect(2)]
        public IDataResult<MemberDetailDto> GetMemberRemainingDaysForScanNumber(string scanNumber)
        {
            using (GymContext context = new GymContext())
            {
                // Şifreli QR kodu çöz
                var decryptResult = _qrCodeEncryptionService.DecryptQrToken(scanNumber);

                // QR kod çözülemezse hata döndür
                if (!decryptResult.Success || decryptResult.Data == null)
                {
                    return new ErrorDataResult<MemberDetailDto>(null, decryptResult.Message ?? "Geçersiz QR kod.");
                }

                var decryptedData = decryptResult.Data;

                // Zaman geçerliliğini kontrol et
                if (!decryptedData.IsValid)
                {
                    return new ErrorDataResult<MemberDetailDto>(null, decryptedData.ErrorMessage ?? "QR kodun süresi dolmuş.");
                }

                // Member ID ile üyeyi bul
                var member = context.Members.FirstOrDefault(m => m.MemberID == decryptedData.MemberId && m.IsActive == true);

                // Ek güvenlik: ScanNumber da kontrol et
                if (member == null || member.ScanNumber != decryptedData.ScanNumber)
                {
                    return new ErrorDataResult<MemberDetailDto>(null, "Üye bulunamadı veya QR kod eşleşmiyor.");
                }

                // Üyelikleri sorgula (CompanyID filtresi olmadan)
                var membershipsQuery = context.Memberships.Where(m => m.MemberID == member.MemberID && m.IsActive == true);

                var activeMemberships = membershipsQuery.ToList();
                var now = DateTime.Now;

                // Aktif üyeliklerde dondurulmuş üyelik var mı kontrol et
                var frozenMembership = activeMemberships.FirstOrDefault(m => m.IsFrozen);
                if (frozenMembership != null)
                {
                    return new ErrorDataResult<MemberDetailDto>(
                        new MemberDetailDto
                        {
                            MemberID = member.MemberID,
                            MemberName = member.Name,
                            PhoneNumber = member.PhoneNumber,
                            Message = $"Üyeliğiniz dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}",
                            Memberships = new List<MembershipDetailDto>()
                        }
                    );
                }

                var validMemberships = activeMemberships
                    .Where(m => m.EndDate > now)
                    .ToList();

                if (validMemberships.Count == 0)
                {
                    var expiredMembershipDto = new MemberDetailDto
                    {
                        MemberID = member.MemberID,
                        MemberName = member.Name,
                        PhoneNumber = member.PhoneNumber,
                        Message = "Üyeliğinizin süresi dolmuştur.",
                        Memberships = new List<MembershipDetailDto>()
                    };
                    return new SuccessDataResult<MemberDetailDto>(expiredMembershipDto);
                }

                // Üyelik tiplerini sorgula (CompanyID filtresi olmadan)
                var membershipTypesQuery = context.MembershipTypes.AsQueryable();

                var membershipTypes = membershipTypesQuery.ToDictionary(mt => mt.MembershipTypeID);

                var membershipDetails = validMemberships.Select(membership =>
                {
                    var membershipType = membershipTypes.ContainsKey(membership.MembershipTypeID)
                        ? membershipTypes[membership.MembershipTypeID]
                        : null;

                    int remainingDays;

                    if (membership.StartDate <= now && membership.EndDate > now)
                    {
                        var remainingTime = membership.EndDate - now;
                        remainingDays = (int)Math.Ceiling(remainingTime.TotalDays);
                    }
                    else
                    {
                        remainingDays = 0;
                    }

                    return new MembershipDetailDto
                    {
                        StartDate = membership.StartDate,
                        EndDate = membership.EndDate,
                        RemainingDays = remainingDays,
                        Branch = membershipType?.Branch
                    };
                }).ToList();

                var memberDetail = new MemberDetailDto
                {
                    MemberID = member.MemberID,
                    MemberName = member.Name,
                    PhoneNumber = member.PhoneNumber,
                    Memberships = membershipDetails
                };

                var membershipToUse = validMemberships.First();

                if (membershipToUse.StartDate > now)
                {
                    memberDetail.Message = $"Üyeliğinizin başlama tarihi: {membershipToUse.StartDate:dd.MM.yyyy}";
                    return new SuccessDataResult<MemberDetailDto>(memberDetail);
                }

                // Giriş çıkış geçmişini sorgula (CompanyID filtresi olmadan)
                var entryHistoryQuery = context.EntryExitHistories
                    .Where(e => e.MembershipID == membershipToUse.MembershipID);

                var lastEntryHistory = entryHistoryQuery
                    .OrderByDescending(e => e.EntryDate)
                    .FirstOrDefault();

                if (lastEntryHistory == null || lastEntryHistory.ExitDate.HasValue)
                {
                    var entryHistory = new EntryExitHistory
                    {
                        MembershipID = membershipToUse.MembershipID,
                        EntryDate = now,
                        IsActive = true,
                        CreationDate = now
                    };
                    _entryExitHistoryService.AddWithCompanyId(entryHistory, member.CompanyID);
                    memberDetail.Message = "Hoşgeldiniz " + member.Name.ToUpper();
                }
                    else if (lastEntryHistory.EntryDate.HasValue)
                    {
                        var timeSinceLastEntry = now - lastEntryHistory.EntryDate.Value;
                        if (timeSinceLastEntry.TotalMinutes > 301)
                        {
                            lastEntryHistory.ExitDate = lastEntryHistory.EntryDate.Value.AddMinutes(301);
                            lastEntryHistory.IsActive = false;
                            lastEntryHistory.UpdatedDate = now;
                            _entryExitHistoryService.UpdateWithCompanyId(lastEntryHistory, member.CompanyID);

                            var newEntryHistory = new EntryExitHistory
                            {
                                MembershipID = membershipToUse.MembershipID,
                                EntryDate = now,
                                IsActive = true,
                                CreationDate = now
                            };
                            _entryExitHistoryService.AddWithCompanyId(newEntryHistory, member.CompanyID);
                            memberDetail.Message = "Hoşgeldiniz " + member.Name.ToUpper();
                        }
                        else
                        {
                            lastEntryHistory.ExitDate = now;
                            lastEntryHistory.IsActive = false;
                            lastEntryHistory.UpdatedDate = now;
                            _entryExitHistoryService.UpdateWithCompanyId(lastEntryHistory, member.CompanyID);
                            memberDetail.Message = "Güle güle " + member.Name.ToUpper();
                        }
                    }
                else
                {
                    var newEntryHistory = new EntryExitHistory
                    {
                        MembershipID = membershipToUse.MembershipID,
                        EntryDate = now,
                        IsActive = true,
                        CreationDate = now
                    };
                    _entryExitHistoryService.AddWithCompanyId(newEntryHistory, member.CompanyID);
                    memberDetail.Message = "Hoşgeldiniz. " + member.Name.ToUpper();
                }

                return new SuccessDataResult<MemberDetailDto>(memberDetail);
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByPhoneNumber(string phoneNumber)
        {
            using (GymContext context = new GymContext())
            {
                // QR kod taraması için CompanyID kontrolü yapmadan tüm üyelerde arama yap
                var query = context.Members.AsQueryable();

                // Önce IsActive=true olan üyeleri kontrol et
                var member = query.FirstOrDefault(m => m.PhoneNumber == phoneNumber && m.IsActive == true);

                // Eğer IsActive=true olan üye bulunamazsa, null döndür
                if (member == null)
                {
                    return new ErrorDataResult<GetMemberQRByPhoneNumberDto>("Telefon Numarasını Kontrol Ediniz.");
                }

                var now = DateTime.Now;

                // Üyelikleri sorgula (CompanyID filtresi olmadan)
                var allMembershipsQuery = context.Memberships
                    .Where(m => m.MemberID == member.MemberID && m.IsActive == true);

                var allMemberships = allMembershipsQuery.OrderBy(m => m.StartDate).ToList();

                // Üyelik tiplerini sorgula (CompanyID filtresi olmadan)
                var membershipTypesQuery = context.MembershipTypes.AsQueryable();

                var membershipTypes = membershipTypesQuery.ToDictionary(mt => mt.MembershipTypeID);

                var consolidatedMemberships = new Dictionary<string, MembershipInfo>();

                // Dondurulmuş üyelik kontrolü
                var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                if (frozenMembership != null)
                {
                    return new ErrorDataResult<GetMemberQRByPhoneNumberDto>(
                        new GetMemberQRByPhoneNumberDto
                        {
                            Name = member.Name,
                            ScanNumber = member.ScanNumber,
                            IsFrozen = true,
                            FreezeEndDate = frozenMembership.FreezeEndDate,
                            RemainingDays = "Dondurulmuş",
                            Memberships = new List<MembershipInfo>()
                        },
                        $"Üyeliğiniz dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}"
                    );
                }

                foreach (var membership in allMemberships)
                {
                    if (!membershipTypes.ContainsKey(membership.MembershipTypeID))
                        continue;

                    var membershipType = membershipTypes[membership.MembershipTypeID];
                    var branch = membershipType.Branch;

                    if (!consolidatedMemberships.ContainsKey(branch))
                    {
                        consolidatedMemberships[branch] = new MembershipInfo
                        {
                            Branch = branch,
                            StartDate = membership.StartDate,
                            EndDate = membership.EndDate,
                            RemainingDays = 0
                        };
                    }

                    var existingMembership = consolidatedMemberships[branch];

                    if (membership.StartDate < existingMembership.StartDate)
                    {
                        existingMembership.StartDate = membership.StartDate;
                    }

                    if (membership.EndDate > existingMembership.EndDate)
                    {
                        existingMembership.EndDate = membership.EndDate;
                    }

                    int remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);
                    existingMembership.RemainingDays += Math.Max(0, remainingDays);
                }

                string message;
                if (consolidatedMemberships.Count == 0)
                {
                    message = "Üyeliğinizin Süresi Dolmuştur";
                }
                else if (consolidatedMemberships.All(m => m.Value.StartDate > now))
                {
                    var earliestMembership = consolidatedMemberships.Values.OrderBy(m => m.StartDate).First();
                    message = $"üyeliğinizin başlamasına {(int)(earliestMembership.StartDate - DateTime.Today).TotalDays} gün vardır.";
                }
                else
                {
                    message = "üyeliğiniz aktif durumdadır.";
                }

                // Şifreli QR kod oluştur
                string encryptedQRCode = _qrCodeEncryptionService.CreateEncryptedQrToken(member.MemberID, member.ScanNumber);

                return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                    new GetMemberQRByPhoneNumberDto
                    {
                        Name = member.Name,
                        ScanNumber = encryptedQRCode, // Şifreli QR kodu kullan
                        RemainingDays = string.Join(", ", consolidatedMemberships.Values
                            .OrderByDescending(m => m.RemainingDays)
                            .Select(m => $"{m.Branch}: {m.RemainingDays} Gün")),
                        Memberships = consolidatedMemberships.Values.ToList(),
                        IsFrozen = false,
                        FreezeEndDate = null
                    },
                    message
                );
            }
        }

        [SecuredOperation("owner,admin")]
        public IDataResult<List<Member>> GetByMemberId(int memberid)
        {
            return new SuccessDataResult<List<Member>>(_memberDal.GetAll(c => c.MemberID == memberid));
        }

        //SECUREDOPERATION ASPECT KOYMA PUBLIC PING STATION
        private string GenerateUniqueQRCode()
        {
            string qrCode;
            do
            {
                var prefix = "MBR";
                var timestamp = DateTime.Now.ToString("yyMMddHH");
                var randomPart = GenerateRandomPart(12);
                qrCode = $"{prefix}{timestamp}{randomPart}";
            } while (_memberDal.GetMemberByScanNumber(qrCode) != null);

            return qrCode;
        }

        // Yeni kullanıcı oluşturma metodu
        private void CreateNewUserForMember(Member member)
        {
            if (string.IsNullOrEmpty(member.PhoneNumber) || member.PhoneNumber.Length < 4)
                return;

            // Telefon numarasının son 4 hanesini geçici şifre olarak kullan
            string tempPassword = member.PhoneNumber.Substring(member.PhoneNumber.Length - 4);

            // Ad ve soyad ayırma (boşluğa göre)
            string firstName = member.Name;
            string lastName = "";

            int spaceIndex = member.Name.IndexOf(' ');
            if (spaceIndex > 0)
            {
                firstName = member.Name.Substring(0, spaceIndex);
                lastName = member.Name.Substring(spaceIndex + 1);
            }

            // Kullanıcı oluştur
            byte[] passwordHash, passwordSalt;
            Core.Utilities.Security.Hashing.HashingHelper.CreatePasswordHash(tempPassword, out passwordHash, out passwordSalt);

            var user = new User
            {
                Email = member.Email,
                FirstName = firstName,
                LastName = lastName,
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsActive = true,
                RequirePasswordChange = true, // Şifre değiştirme zorunluluğu
                CreationDate = DateTime.Now
            };

            _userService.Add(user);

            // "member" rolünü al
            var operationClaimService = new OperationClaimManager(new EfOperationClaimDal());
            var memberRoleResult = operationClaimService.GetByName("member");

            if (memberRoleResult.Success && memberRoleResult.Data != null)
            {
                // Kullanıcıya "member" rolünü ata
                var userOperationClaimService = new UserOperationClaimManager(
                    new EfUserOperationClaimDal(),
                    _userDeviceService,
                    _httpContextAccessor);
                var userOperationClaim = new UserOperationClaim
                {
                    UserId = user.UserID,
                    OperationClaimId = memberRoleResult.Data.OperationClaimId,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };
                userOperationClaimService.AddForRegistration(userOperationClaim);
            }

            // Member ile User'ı ilişkilendir
            member.UserID = user.UserID;
        }

        //SECUREDOPERATION ASPECT KOYMA PUBLIC PING STATION
        private string GenerateRandomPart(int length)
        {
            byte[] data = new byte[length];
            using (var rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(data);
            }
            return new string(data.Select(b => Chars[b % Chars.Length]).ToArray());
        }


        //Şuanda kullanılmayan bir metot ilerde lazım olabilir diye duruyor.
        [SecuredOperation("owner,admin,member")]
        [PerformanceAspect(3)]
        public IDataResult<Member> GetMemberByUserId(int userId)
        {
            var member = _memberDal.Get(m => m.UserID == userId && m.IsActive == true);
            if (member == null)
            {
                return new ErrorDataResult<Member>(Messages.MemberNotFound);
            }
            return new SuccessDataResult<Member>(member);
        }

        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        public IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByUserIdWithoutCompanyFilter(int userId)
        {
            using (GymContext context = new GymContext())
            {
                // Kullanıcının kendi UserID'sine göre Member tablosundan ilgili üyeyi bul
                // CompanyID filtrelemesi yapmadan tüm üyelerde arama yap
                var member = context.Members.FirstOrDefault(m => m.UserID == userId && m.IsActive == true);

                // Eğer üye bulunamazsa, hata döndür
                if (member == null)
                {
                    return new ErrorDataResult<GetMemberQRByPhoneNumberDto>("Üyelik bilgileriniz bulunamadı. Lütfen spor salonunuzla iletişime geçin.");
                }

                var now = DateTime.Now;

                // Üyelikleri sorgula (CompanyID filtrelemesi yapmadan)
                var allMemberships = context.Memberships
                    .Where(m => m.MemberID == member.MemberID && m.IsActive == true)
                    .OrderBy(m => m.StartDate)
                    .ToList();

                // Üyelik tiplerini sorgula (CompanyID filtrelemesi yapmadan)
                var membershipTypes = context.MembershipTypes.ToDictionary(mt => mt.MembershipTypeID);

                var consolidatedMemberships = new Dictionary<string, MembershipInfo>();

                // Dondurulmuş üyelik kontrolü
                var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                if (frozenMembership != null)
                {
                    return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                        new GetMemberQRByPhoneNumberDto
                        {
                            Name = member.Name,
                            ScanNumber = member.ScanNumber,
                            IsFrozen = true,
                            FreezeEndDate = frozenMembership.FreezeEndDate,
                            RemainingDays = "Dondurulmuş",
                            Memberships = new List<MembershipInfo>(),
                            PhoneNumber = member.PhoneNumber
                        },
                        $"Üyeliğiniz dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}"
                    );
                }

                foreach (var membership in allMemberships)
                {
                    if (!membershipTypes.ContainsKey(membership.MembershipTypeID))
                        continue;

                    var membershipType = membershipTypes[membership.MembershipTypeID];
                    var branch = membershipType.Branch;

                    if (!consolidatedMemberships.ContainsKey(branch))
                    {
                        consolidatedMemberships[branch] = new MembershipInfo
                        {
                            Branch = branch,
                            StartDate = membership.StartDate,
                            EndDate = membership.EndDate,
                            RemainingDays = 0
                        };
                    }

                    var existingMembership = consolidatedMemberships[branch];

                    if (membership.StartDate < existingMembership.StartDate)
                    {
                        existingMembership.StartDate = membership.StartDate;
                    }

                    if (membership.EndDate > existingMembership.EndDate)
                    {
                        existingMembership.EndDate = membership.EndDate;
                    }

                    int remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);
                    existingMembership.RemainingDays += Math.Max(0, remainingDays);
                }

                string message;
                if (consolidatedMemberships.Count == 0)
                {
                    message = "Üyeliğinizin Süresi Dolmuştur";
                }
                else if (consolidatedMemberships.All(m => m.Value.StartDate > now))
                {
                    var earliestMembership = consolidatedMemberships.Values.OrderBy(m => m.StartDate).First();
                    message = $"Üyeliğinizin başlamasına {(int)(earliestMembership.StartDate - DateTime.Today).TotalDays} gün vardır.";
                }
                else
                {
                    message = "Üyeliğiniz aktif durumdadır.";
                }

                // Şifreli QR kod oluştur
                string encryptedQRCode = _qrCodeEncryptionService.CreateEncryptedQrToken(member.MemberID, member.ScanNumber);

                return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                    new GetMemberQRByPhoneNumberDto
                    {
                        Name = member.Name,
                        ScanNumber = encryptedQRCode, // Şifreli QR kodu kullan
                        RemainingDays = string.Join(", ", consolidatedMemberships.Values
                            .OrderByDescending(m => m.RemainingDays)
                            .Select(m => $"{m.Branch}: {m.RemainingDays} Gün")),
                        Memberships = consolidatedMemberships.Values.ToList(),
                        IsFrozen = false,
                        FreezeEndDate = null,
                        PhoneNumber = member.PhoneNumber
                    },
                    message
                );
            }
        }

        /// <summary>
        /// Kullanıcının profil bilgilerini getirir (sadece member rolü)
        /// </summary>
        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        public IDataResult<MemberProfileDto> GetMemberProfileByUserId(int userId)
        {
            using (GymContext context = new GymContext())
            {
                // User bilgilerini al
                var user = context.Users.FirstOrDefault(u => u.UserID == userId && u.IsActive);
                if (user == null)
                {
                    return new ErrorDataResult<MemberProfileDto>("Kullanıcı bulunamadı.");
                }

                // Member bilgilerini al
                var member = context.Members.FirstOrDefault(m => m.UserID == userId && m.IsActive == true);
                if (member == null)
                {
                    return new ErrorDataResult<MemberProfileDto>("Üyelik bilgileriniz bulunamadı. Lütfen spor salonunuzla iletişime geçin.");
                }

                var profileDto = new MemberProfileDto
                {
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    Adress = member.Adress,
                    BirthDate = member.BirthDate,
                    PhoneNumber = member.PhoneNumber,
                    ProfileImagePath = user.ProfileImagePath
                };

                return new SuccessDataResult<MemberProfileDto>(profileDto, "Profil bilgileri başarıyla getirildi.");
            }
        }

        /// <summary>
        /// Kullanıcının profil bilgilerini günceller (sadece member rolü)
        /// User tablosunda: FirstName, LastName (tek kayıt)
        /// Member tablosunda: Adress, BirthDate (kullanıcının kayıtlı olduğu TÜM salonlarda güncellenir)
        /// Not: Member.Name alanı güncellenmez (salon yöneticisi kontrolünde)
        /// </summary>
        [SecuredOperation("member")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Member")]
        public IResult UpdateMemberProfile(int userId, MemberProfileUpdateDto profileUpdateDto)
        {
            using (GymContext context = new GymContext())
            {
                // User bilgilerini al ve güncelle
                var user = context.Users.FirstOrDefault(u => u.UserID == userId && u.IsActive);
                if (user == null)
                {
                    return new ErrorResult("Kullanıcı bulunamadı.");
                }

                // Kullanıcının TÜM Member kayıtlarını al (çoklu salon üyeliği için)
                var members = context.Members.Where(m => m.UserID == userId && m.IsActive == true).ToList();
                if (!members.Any())
                {
                    return new ErrorResult("Üyelik bilgileriniz bulunamadı. Lütfen spor salonunuzla iletişime geçin.");
                }

                // User tablosunu güncelle (FirstName, LastName)
                if (!string.IsNullOrWhiteSpace(profileUpdateDto.FirstName))
                {
                    user.FirstName = profileUpdateDto.FirstName.Trim();
                }
                if (!string.IsNullOrWhiteSpace(profileUpdateDto.LastName))
                {
                    user.LastName = profileUpdateDto.LastName.Trim();
                }
                user.UpdatedDate = DateTime.Now;

                // TÜM Member kayıtlarını güncelle (Adress, BirthDate)
                // Not: Member tablosundaki Name alanını güncellemiyoruz (spor salonu yönetimi kontrolü)
                foreach (var member in members)
                {
                    member.Adress = profileUpdateDto.Adress?.Trim();
                    member.BirthDate = profileUpdateDto.BirthDate;
                    member.UpdatedDate = DateTime.Now;
                }

                // Değişiklikleri kaydet
                context.Users.Update(user);
                context.Members.UpdateRange(members); // Toplu güncelleme
                context.SaveChanges();

                return new SuccessResult("Profil bilgileriniz başarıyla güncellendi.");
            }
        }

        /// <summary>
        /// Çoklu branş üye listesi - Sayfalama olmadan
        /// </summary>
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Member", "MultiBranch")]
        public IDataResult<List<MultiBranchMemberDto>> GetMultiBranchMembers()
        {
            return new SuccessDataResult<List<MultiBranchMemberDto>>(_memberDal.GetMultiBranchMembers());
        }

        /// <summary>
        /// Çoklu branş üye listesi - Sayfalama ile
        /// </summary>
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MultiBranchMemberDto>> GetMultiBranchMembersPaginated(MemberPagingParameters parameters)
        {
            var result = _memberDal.GetMultiBranchMembersPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MultiBranchMemberDto>>(result);
        }
    }
}