using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Çoklu üyelik silme işlemi için DTO
    /// </summary>
    public class MembershipDeleteDto : IDto
    {
        public int MembershipID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public int PaymentCount { get; set; }
        public bool HasActivePayments { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// Üye silme seçenekleri için DTO
    /// </summary>
    public class MemberDeleteOptionsDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public List<MembershipDeleteDto> ActiveMemberships { get; set; } = new List<MembershipDeleteDto>();
        public bool HasMultipleMemberships => ActiveMemberships.Count > 1;
    }
}
