import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON>roy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { MemberService } from '../../services/member.service';
import { MembershipType } from '../../models/membershipType';
import { MembershipTypeService } from '../../services/membership-type.service';
import { MatDialog } from '@angular/material/dialog';
import { MembershipUpdateComponent } from '../crud/membership-update/membership-update.component';
import { faEdit, faSnowflake, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
import { MembershipService } from '../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { MemberFilter } from '../../models/memberFilter';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { FreezeMembershipDialogComponent } from '../freeze-membership-dialog/freeze-membership-dialog.component';
import { DialogService } from '../../services/dialog.service';
import { Chart, ChartConfiguration, ChartType } from 'chart.js';
import { MultiBranchMember, MemberDeleteOptions } from '../../models/multi-branch-member.model';
import { MemberDeleteOptionsDialogComponent } from '../member-delete-options-dialog/member-delete-options-dialog.component';

@Component({
    selector: 'app-member-filter',
    templateUrl: './member-filter.component.html',
    styleUrls: ['./member-filter.component.css'],
    standalone: false
})
export class MemberFilterComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('genderChart') genderChartRef: ElementRef;
  genderChart: Chart;
  members: MemberFilter[] = [];
  multiBranchMembers: MultiBranchMember[] = [];
  activeMembersCount: number = 0;
  memberFilterText: string = '';
  private searchTextSubject = new Subject<string>();
  genderFilter: string = '';
  branchFilter: string = '';
  membershipTypes: MembershipType[] = [];
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faSnowflake = faSnowflake;
  isLoading: boolean = false;
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  totalActiveMembers: number = 0;

  // Çoklu branş görünüm modu
  isMultiBranchView: boolean = false;

  genderCounts = {
    all: 0,
    male: 0,
    female: 0
  };
  branchCounts: { [key: string]: number } = {};

  constructor(
    private memberService: MemberService,
    private membershipTypeService: MembershipTypeService,
    private membershipService: MembershipService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService 
  ) {
    this.searchTextSubject.pipe(
      debounceTime(750),
      distinctUntilChanged()
    ).subscribe(searchText => {
      this.memberFilterText = searchText;
      this.currentPage = 1;
      if (this.isMultiBranchView) {
        this.loadMultiBranchMembers();
      } else {
        this.loadMembers();
      }
    });
  }

  ngOnInit(): void {
    this.getBranches();
    this.loadMembers();
    this.getTotalActiveMembers();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.initGenderChart();
    }, 500);
  }

  ngOnDestroy(): void {
    this.searchTextSubject.complete();
    if (this.genderChart) {
      this.genderChart.destroy();
    }
  }

  initGenderChart(): void {
    const canvas = document.getElementById('genderChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    if (this.genderChart) {
      this.genderChart.destroy();
    }

    const chartData = {
      labels: ['Erkek', 'Kadın'],
      datasets: [{
        data: [this.genderCounts.male, this.genderCounts.female],
      backgroundColor: [
        'rgba(67, 97, 238, 0.7)',  // Erkek - Mavi
        'rgba(255, 105, 180, 0.7)'   // Kadın - Pembe
      ],
      borderColor: [
        'rgb(67, 97, 238)',
        'rgb(255, 105, 180)'
        ],
        borderWidth: 1,
        hoverOffset: 4
      }]
    };

    const config: ChartConfiguration = {
      type: 'doughnut' as ChartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              pointStyle: 'circle',
              font: {
                size: 12
              }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw as number;
                const total = (context.dataset.data as number[]).reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        },
        animation: {
          duration: 1000
        },
        layout: {
          padding: {
            top: 10,
            bottom: 20
          }
        }
      }
    };

    this.genderChart = new Chart(ctx, config);
  }

  searchTextChanged(text: string) {
    this.searchTextSubject.next(text);
  }

  onFilterChange(): void {
    this.currentPage = 1;
    if (this.isMultiBranchView) {
      this.loadMultiBranchMembers();
    } else {
      this.loadMembers();
    }
    this.getTotalActiveMembers();
  }

  // Görünüm modunu değiştir
  toggleViewMode(): void {
    this.isMultiBranchView = !this.isMultiBranchView;
    this.currentPage = 1;
    this.memberFilterText = '';
    this.genderFilter = '';
    this.branchFilter = '';

    if (this.isMultiBranchView) {
      this.loadMultiBranchMembers();
    } else {
      this.loadMembers();
    }
  }
  openFreezeDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(FreezeMembershipDialogComponent, {
      width: '400px',
      data: { 
        memberName: member.name,
        membershipID: member.membershipID
      }
    });

    dialogRef.afterClosed().subscribe(freezeDays => {
      if (freezeDays) {
        this.membershipService.freezeMembership(member.membershipID, freezeDays).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Üyelik başarıyla donduruldu');
              this.loadMembers();
            } else {
              this.toastrService.error(response.message);
            }
          },
          error: (error) => {
            this.toastrService.error('Üyelik dondurulurken bir hata oluştu');
          }
        });
      }
    });
  }
  getTotalActiveMembers() {
    this.memberService.getTotalActiveMembers().subscribe({
      next: (response) => {
        if (response.success) {
          this.totalActiveMembers = response.data;
          this.activeMembersCount = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching total members:', error);
      },
    });
  }

  loadMembers() {
    this.isLoading = true;
    const gender = this.genderFilter ? parseInt(this.genderFilter) : undefined;

    this.memberService
      .getMemberDetailsPaginated(
        this.currentPage,
        this.memberFilterText,
        gender,
        this.branchFilter
      )
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.members = response.data.data;
            this.totalPages = response.data.totalPages;
            this.totalItems = response.data.totalCount;
            this.calculateActiveMembersCount();
            this.calculateFilterCounts();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching members:', error);
          this.toastrService.error(
            'Üyeler yüklenirken bir hata oluştu.',
            'Hata'
          );
          this.isLoading = false;
        },
      });
  }

  // Çoklu branş üyeleri yükle
  loadMultiBranchMembers() {
    this.isLoading = true;
    const gender = this.genderFilter ? parseInt(this.genderFilter) : undefined;

    this.memberService
      .getMultiBranchMembers(
        this.currentPage,
        this.memberFilterText,
        gender,
        this.branchFilter
      )
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.multiBranchMembers = response.data.data;
            this.totalPages = response.data.totalPages;
            this.totalItems = response.data.totalCount;
            this.calculateFilterCounts();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching multi-branch members:', error);
          this.toastrService.error(
            'Çoklu branş üyeleri yüklenirken bir hata oluştu.',
            'Hata'
          );
          this.isLoading = false;
        },
      });
  }

  calculateFilterCounts() {
    this.genderCounts.all = this.totalItems;
  
    this.memberService.getActiveMemberCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.genderCounts.male = response.data['male'];
          this.genderCounts.female = response.data['female'];
          
          // Cinsiyet grafiğini güncelle
          if (this.genderChart) {
            this.genderChart.data.datasets[0].data = [
              this.genderCounts.male, 
              this.genderCounts.female
            ];
            this.genderChart.update();
          } else {
            this.initGenderChart();
          }
        }
      },
      error: (error) => {
        console.error('Error fetching gender counts:', error);
      }
    });
  
    this.memberService.getBranchCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.branchCounts = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching branch counts:', error);
      }
    });
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      if (this.isMultiBranchView) {
        this.loadMultiBranchMembers();
      } else {
        this.loadMembers();
      }
    }
  }

  getBranches() {
    this.membershipTypeService.getMembershipTypes().subscribe((response) => {
      this.membershipTypes = this.getUniqueBranches(response.data);
    });
  }

  getUniqueBranches(membershipTypes: MembershipType[]): MembershipType[] {
    const uniqueBranches: MembershipType[] = [];
    const branchMap = new Map<string, boolean>();

    membershipTypes.forEach((type) => {
      if (!branchMap.has(type.branch)) {
        branchMap.set(type.branch, true);
        uniqueBranches.push(type);
      }
    });

    return uniqueBranches;
  }

  calculateActiveMembersCount() {
    this.activeMembersCount = this.members.filter((member) => {
      return member.remainingDays >= 0;
    }).length;
  }

  openUpdateDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(MembershipUpdateComponent, {
      width: '400px',
      data: {
        membershipID: member.membershipID,
        memberID: member.memberID,
        membershipTypeID: member.membershipTypeID,
        startDate: member.startDate,
        endDate: member.endDate,
        name: member.name,
        branch: member.branch,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadMembers();
      }
    });
  }

  deleteMember(member: MemberFilter) {
    this.dialogService.confirmMembershipDelete(member.name, member).subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.membershipService.delete(member.membershipID).subscribe(
          (response) => {
            this.isLoading = false;
            if (response.success) {
              this.toastrService.success(response.message, 'Başarılı');
              this.loadMembers();
              this.getTotalActiveMembers();
            } else {
              this.toastrService.error(response.message, 'Hata');
            }
          },
          (error) => {
            this.isLoading = false;
            this.toastrService.error('Üye silinirken bir hata oluştu.', 'Hata');
          }
        );
      }
    });
  }

  // Çoklu branş üye silme
  deleteMultiBranchMember(member: MultiBranchMember) {
    const dialogRef = this.dialog.open(MemberDeleteOptionsDialogComponent, {
      width: '500px',
      data: {
        memberName: member.name,
        memberId: member.memberId,
        memberships: member.memberships
      }
    });

    dialogRef.afterClosed().subscribe((result: MemberDeleteOptions | null) => {
      if (result) {
        this.isLoading = true;

        if (result.deleteType === 'all') {
          // Tüm üyelikleri sil
          this.memberService.deleteAllMemberships(member.memberId).subscribe({
            next: (response) => {
              this.isLoading = false;
              if (response.success) {
                this.toastrService.success('Tüm üyelikler başarıyla silindi', 'Başarılı');
                this.loadMultiBranchMembers();
                this.getTotalActiveMembers();
              } else {
                this.toastrService.error(response.message, 'Hata');
              }
            },
            error: (error) => {
              this.isLoading = false;
              this.toastrService.error('Üyelikler silinirken bir hata oluştu.', 'Hata');
            }
          });
        } else if (result.deleteType === 'selected' && result.selectedMembershipIds) {
          // Seçili üyelikleri sil
          this.memberService.deleteSelectedMemberships(result.selectedMembershipIds).subscribe({
            next: (response) => {
              this.isLoading = false;
              if (response.success) {
                this.toastrService.success('Seçili üyelikler başarıyla silindi', 'Başarılı');
                this.loadMultiBranchMembers();
                this.getTotalActiveMembers();
              } else {
                this.toastrService.error(response.message, 'Hata');
              }
            },
            error: (error) => {
              this.isLoading = false;
              this.toastrService.error('Üyelikler silinirken bir hata oluştu.', 'Hata');
            }
          });
        }
      }
    });
  }

  // Aktif üyelik sayısını hesapla
  getActiveMembershipsCount(member: MultiBranchMember): number {
    return member.memberships.filter(m => m.isActive && m.remainingDays > 0).length;
  }

  // Süresi dolmuş üyelik sayısını hesapla
  getExpiredMembershipsCount(member: MultiBranchMember): number {
    return member.memberships.filter(m => m.remainingDays <= 0).length;
  }
}