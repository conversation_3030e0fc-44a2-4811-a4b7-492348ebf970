/* Member Delete Options Dialog Styles */

.delete-options-dialog {
  max-width: 600px;
  width: 100%;
}

.dialog-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid var(--border-color);
}

.dialog-title {
  margin: 0 0 12px;
  color: var(--text-primary);
  font-weight: 600;
}

.dialog-subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.dialog-body {
  padding: 24px;
  max-height: 500px;
  overflow-y: auto;
}

.membership-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.membership-option {
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.membership-option:hover {
  border-color: var(--primary);
  background-color: var(--hover-bg);
}

.membership-option.selected {
  border-color: var(--primary);
  background-color: rgba(0, 123, 255, 0.05);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.selection-radio {
  margin-top: 4px;
}

.selection-radio input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary);
}

.membership-info {
  flex: 1;
}

.membership-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.membership-type {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.membership-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.detail-label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 14px;
  min-width: 120px;
}

.detail-value {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  text-align: right;
}

.payment-warning {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
}

.warning-text {
  font-size: 13px;
  color: var(--warning);
  font-weight: 500;
}

.info-box {
  padding: 16px;
  background-color: rgba(23, 162, 184, 0.1);
  border: 1px solid rgba(23, 162, 184, 0.3);
  border-radius: 8px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-content {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.dialog-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* Branch Badge Styles */
.badge-fitness {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.badge-crossfit {
  background: linear-gradient(135deg, #dc3545, #a71e2a);
  color: white;
}

.badge-pilates {
  background: linear-gradient(135deg, #e91e63, #ad1457);
  color: white;
}

.badge-yoga {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.badge-boxing {
  background: linear-gradient(135deg, #fd7e14, #e55a00);
  color: white;
}

.badge-default {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

.badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Button Styles */
.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-danger {
  background-color: var(--danger);
  border-color: var(--danger);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-secondary {
  background-color: var(--secondary);
  border-color: var(--secondary);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
  .delete-options-dialog {
    max-width: 95vw;
    margin: 10px;
  }
  
  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding: 16px;
  }
  
  .membership-option {
    padding: 12px;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-label {
    min-width: auto;
  }
  
  .detail-value {
    text-align: left;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}

/* Dark Mode Support */
[data-theme="dark"] .membership-option {
  background-color: var(--card-bg-dark);
  border-color: var(--border-color-dark);
}

[data-theme="dark"] .membership-option:hover {
  background-color: var(--hover-bg-dark);
}

[data-theme="dark"] .membership-option.selected {
  background-color: rgba(0, 123, 255, 0.15);
}

[data-theme="dark"] .info-box {
  background-color: rgba(23, 162, 184, 0.15);
  border-color: rgba(23, 162, 184, 0.4);
}

[data-theme="dark"] .payment-warning {
  background-color: rgba(255, 193, 7, 0.15);
  border-color: rgba(255, 193, 7, 0.4);
}

/* Animation */
.membership-option {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
