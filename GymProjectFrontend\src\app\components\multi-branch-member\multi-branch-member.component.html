<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="modern-card">
        <div class="card-header">
          <h5><i class="fas fa-users me-2"></i>Çoklu Branş Üye Yönetimi</h5>
          <div class="d-flex align-items-center gap-3">
            <span class="modern-badge modern-badge-primary">
              <i class="fas fa-users me-2"></i>
              Toplam: {{ totalItems }} Üye
            </span>
            <button class="btn-modern btn-modern-primary" (click)="refreshMembers()">
              <fa-icon [icon]="faSyncAlt" [class.fa-spin]="isLoading"></fa-icon>
              Yenile
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="modern-card">
        <div class="card-body">
          <div class="row g-3">
            <!-- Search -->
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-search me-2"></i>Arama
                </label>
                <input
                  type="text"
                  class="form-control modern-input"
                  placeholder="Ad, soyad veya telefon ile ara..."
                  [value]="searchText"
                  (input)="onSearch($event)"
                />
              </div>
            </div>

            <!-- Gender Filter -->
            <div class="col-md-3">
              <div class="form-group">
                <label class="form-label">
                  <i class="fas fa-venus-mars me-2"></i>Cinsiyet
                </label>
                <select
                  class="form-select modern-select"
                  [(ngModel)]="selectedGender"
                  (change)="onGenderFilterChange()"
                >
                  <option [value]="null">Tümü</option>
                  <option [value]="1">Erkek</option>
                  <option [value]="0">Kadın</option>
                </select>
              </div>
            </div>

            <!-- Branch Filter -->
            <div class="col-md-3">
              <div class="form-group">
                <label class="form-label">
                  <fa-icon [icon]="faDumbbell" class="me-2"></fa-icon>Branş
                </label>
                <select
                  class="form-select modern-select"
                  [(ngModel)]="selectedBranch"
                  (change)="onBranchFilterChange()"
                >
                  <option value="">Tüm Branşlar</option>
                  <option *ngFor="let branch of availableBranches" [value]="branch">
                    {{ branch }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Clear Filters -->
            <div class="col-md-2">
              <div class="form-group">
                <label class="form-label">&nbsp;</label>
                <button
                  class="btn btn-outline-secondary w-100"
                  (click)="clearFilters()"
                >
                  <i class="fas fa-times me-2"></i>Temizle
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Members Table -->
  <div class="row">
    <div class="col-12">
      <div class="modern-card" [class.content-blur]="isLoading">
        <div class="card-body">
          <div class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-user me-2"></i>
                    Üye Bilgileri
                  </th>
                  <th>
                    <i class="fas fa-phone me-2"></i>
                    İletişim
                  </th>
                  <th>
                    <fa-icon [icon]="faDumbbell" class="me-2"></fa-icon>
                    Aktif Üyelikler
                  </th>
                  <th>
                    <i class="fas fa-calendar-day me-2"></i>
                    Toplam Kalan Gün
                  </th>
                  <th>
                    <i class="fas fa-wallet me-2"></i>
                    Bakiye
                  </th>
                  <th>
                    <i class="fas fa-cogs me-2"></i>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let member of members" class="zoom-in">
                  <!-- Üye Bilgileri -->
                  <td>
                    <div class="member-info">
                      <div class="modern-avatar" [ngStyle]="{'background-color': getGenderColor(member.gender)}">
                        <i [class]="getGenderIcon(member.gender)"></i>
                      </div>
                      <div class="member-details">
                        <div class="member-name">{{ member.name }}</div>
                        <small class="text-muted">
                          <i class="fas fa-calendar me-1"></i>
                          {{ member.creationDate | date:'dd.MM.yyyy' }}
                        </small>
                      </div>
                    </div>
                  </td>

                  <!-- İletişim -->
                  <td>
                    <div class="contact-info">
                      <div>
                        <i class="fas fa-phone me-2"></i>
                        {{ member.phoneNumber }}
                      </div>
                      <div *ngIf="member.email" class="text-muted">
                        <i class="fas fa-envelope me-2"></i>
                        {{ member.email }}
                      </div>
                    </div>
                  </td>

                  <!-- Aktif Üyelikler -->
                  <td>
                    <div class="memberships-container">
                      <div *ngFor="let membership of member.memberships" class="membership-badge">
                        <span [class]="'badge ' + getBranchBadgeClass(membership.branch)">
                          {{ membership.branch }}
                          <span class="remaining-days">({{ membership.remainingDays }})</span>
                        </span>
                        <div class="membership-details">
                          <small class="text-muted">
                            {{ membership.typeName }} - 
                            {{ membership.endDate | date:'dd.MM.yyyy' }}
                          </small>
                        </div>
                      </div>
                      <div *ngIf="member.memberships.length === 0" class="text-muted">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Aktif üyelik yok
                      </div>
                    </div>
                  </td>

                  <!-- Toplam Kalan Gün -->
                  <td>
                    <span [class]="'badge ' + getRemainingDaysClass(member.totalRemainingDays)">
                      {{ formatRemainingDays(member.totalRemainingDays) }}
                    </span>
                  </td>

                  <!-- Bakiye -->
                  <td>
                    <span [class]="member.balance >= 0 ? 'text-success' : 'text-danger'">
                      {{ member.balance | currency:'TRY':'symbol':'1.2-2' }}
                    </span>
                  </td>

                  <!-- İşlemler -->
                  <td>
                    <div class="action-buttons">
                      <button
                        class="btn btn-sm btn-outline-info me-2"
                        title="Detayları Görüntüle"
                      >
                        <fa-icon [icon]="faInfoCircle"></fa-icon>
                      </button>
                      <button
                        class="btn btn-sm btn-outline-warning me-2"
                        title="Düzenle"
                      >
                        <fa-icon [icon]="faEdit"></fa-icon>
                      </button>
                      <button
                        class="btn btn-sm btn-outline-danger"
                        title="Sil"
                        (click)="onDeleteMember(member)"
                      >
                        <fa-icon [icon]="faTrashAlt"></fa-icon>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Empty State -->
            <div *ngIf="members.length === 0 && !isLoading" class="empty-state">
              <i class="fas fa-users fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">Üye bulunamadı</h5>
              <p class="text-muted">Arama kriterlerinizi değiştirmeyi deneyin.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div class="row mt-4" *ngIf="totalPages > 1">
    <div class="col-12">
      <nav aria-label="Sayfa navigasyonu">
        <ul class="pagination justify-content-center">
          <li class="page-item" [class.disabled]="currentPage === 1">
            <a class="page-link" (click)="onPageChange(currentPage - 1)">Önceki</a>
          </li>
          <li
            class="page-item"
            *ngFor="let page of [].constructor(totalPages); let i = index"
            [class.active]="currentPage === i + 1"
          >
            <a class="page-link" (click)="onPageChange(i + 1)">{{ i + 1 }}</a>
          </li>
          <li class="page-item" [class.disabled]="currentPage === totalPages">
            <a class="page-link" (click)="onPageChange(currentPage + 1)">Sonraki</a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="loading-spinner">
      <fa-icon [icon]="faSyncAlt" class="fa-spin" size="2x"></fa-icon>
      <p class="mt-2">Yükleniyor...</p>
    </div>
  </div>
</div>
