import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { map, startWith, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Member } from '../../../models/member';
import { MembershipType } from '../../../models/membershipType';
import { MemberService } from '../../../services/member.service';
import { MembershipService } from '../../../services/membership.service';
import { MembershipTypeService } from '../../../services/membership-type.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { MembershipAddRequest } from '../../../models/multi-branch-member.model';

@Component({
  selector: 'app-membership-add',
  templateUrl: './membership-add.component.html',
  styleUrls: ['./membership-add.component.css'],
  standalone: false
})
export class MembershipAddComponent implements OnInit {
  membershipAddForm: FormGroup;
  members: Member[] = [];
  membershipTypes: MembershipType[] = [];
  filteredMembers: Observable<Member[]>;
  filteredMembershipTypes: Observable<MembershipType[]>;
  showBranchList: boolean = false;
  lastMembershipInfo: string | null = null;
  isSubmitting = false;
  isLoading = true; // Sayfa yüklenirken spinner göstermek için

  // Akıllı yenileme için
  isRenewalMode: boolean = false;
  existingMembershipId: number | null = null;
  renewalOptions: any[] = [];
  showRenewalOptions: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private memberService: MemberService,
    private membershipService: MembershipService,
    private membershipTypeService: MembershipTypeService,
    private toastrService: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.createMembershipAddForm();
    this.loadInitialData();
    this.setupMemberAutocomplete();
    this.setupMembershipTypeAutocomplete();
    this.setupMembershipEndDateCalculation();

    // Başlangıç tarihini güncel tarih olarak ayarla
    this.setCurrentDateAsStartDate();
  }

  // Başlangıç tarihini güncel tarih olarak ayarlayan metot
  setCurrentDateAsStartDate(): void {
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0]; // YYYY-MM-DD formatı

    // Form kontrolüne değeri atama
    const startDateControl = this.membershipAddForm.get('startDate');
    if (startDateControl) {
      startDateControl.setValue(formattedDate);
    }
  }

  // Üyelik türlerinin boş olup olmadığını kontrol eden metot
  hasMembershipTypes(): boolean {
    // Üyelik türleri henüz yüklenmemişse veya en az bir üyelik türü varsa true döndür
    return !this.membershipTypes || this.membershipTypes.length > 0;
  }

  // Üyelik türü ekleme sayfasına yönlendirme
  navigateToMembershipTypeAdd(): void {
    this.router.navigate(['/membershiptype/add']);
  }

  createMembershipAddForm() {
    this.membershipAddForm = this.formBuilder.group({
      memberID: ['', Validators.required],
      membershipTypeID: ['', Validators.required],
      startDate: [new Date(), Validators.required],
      endDate: [''],
      day: ['', Validators.required], // Zorunlu yapıldı
      price: ['', Validators.required], // Zorunlu yapıldı
      PaymentMethod: ['', Validators.required],
      paymentStatus: ['Completed'] // Yeni alan eklendi
    });

    const memberIdControl = this.membershipAddForm.get('memberID');
    if (memberIdControl) {
      memberIdControl.valueChanges.subscribe(value => {
        if (value && typeof value === 'object') {
          this.getLastMembershipInfo(value.memberID);
        }
      });
    }
  }

  // Tüm başlangıç verilerini yükleyen metot
  loadInitialData() {
    this.isLoading = true;
    let membersLoaded = false;
    let membershipTypesLoaded = false;

    // Üyeleri yükle
    this.memberService.getMembers().subscribe({
      next: (response) => {
        this.members = response.data;
        membersLoaded = true;
        this.checkDataLoadingComplete(membersLoaded, membershipTypesLoaded);
      },
      error: (error) => {
        console.error('Üyeler yüklenirken hata oluştu:', error);
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu.', 'Hata');
        membersLoaded = true;
        this.checkDataLoadingComplete(membersLoaded, membershipTypesLoaded);
      }
    });

    // Üyelik türlerini yükle
    this.membershipTypeService.getMembershipTypes().subscribe({
      next: (response) => {
        this.membershipTypes = response.data;
        membershipTypesLoaded = true;
        this.checkDataLoadingComplete(membersLoaded, membershipTypesLoaded);
      },
      error: (error) => {
        console.error('Üyelik türleri yüklenirken hata oluştu:', error);
        this.toastrService.error('Üyelik türleri yüklenirken bir hata oluştu.', 'Hata');
        membershipTypesLoaded = true;
        this.checkDataLoadingComplete(membersLoaded, membershipTypesLoaded);
      }
    });
  }

  // Veri yükleme tamamlanma kontrolü
  checkDataLoadingComplete(membersLoaded: boolean, membershipTypesLoaded: boolean) {
    if (membersLoaded && membershipTypesLoaded) {
      this.isLoading = false;
    }
  }

  getMembers() {
    this.memberService.getMembers().subscribe(response => {
      this.members = response.data;
    });
  }

  getMembershipTypes() {
    this.membershipTypeService.getMembershipTypes().subscribe(response => {
      this.membershipTypes = response.data;
    });
  }

  setupMemberAutocomplete() {
    const memberIdControl = this.membershipAddForm.get('memberID');
    if (memberIdControl) {
      this.filteredMembers = memberIdControl.valueChanges.pipe(
      
        distinctUntilChanged(),
        map(value => {
          if (typeof value === 'string') {
            if (!value.trim()) {
              return []; // Boş input durumunda boş liste döndür
            }
            return this._filterMembers(value);
          }
          return []; // Üye seçildiğinde dropdown'ı kapat
        })
      );
    }
  }

  setupMembershipTypeAutocomplete() {
    const membershipTypeControl = this.membershipAddForm.get('membershipTypeID');
    if (membershipTypeControl) {
      this.filteredMembershipTypes = membershipTypeControl.valueChanges.pipe(
        startWith(''),
        map(value => {
          // Always show all membership types
          return this.membershipTypes;
        })
      );

      membershipTypeControl.valueChanges.subscribe(value => {
        if (value) {
          const selectedType = this.membershipTypes.find(
            type => `${type.branch} - ${type.typeName}` === value
          );
          if (selectedType) {
            this.membershipAddForm.patchValue({
              day: selectedType.day,
              price: selectedType.price
            });
            this.calculateEndDate();
          } else if (typeof value === 'string' && value.trim() !== '') {
            // If user types something that's not a valid membership type,
            // reset the field to empty to force selection from dropdown
            setTimeout(() => {
              membershipTypeControl.setValue('');
            }, 0);
          }
        }
      });

      // Üyelik türü kontrolü - toast mesajı kaldırıldı
      // Artık sadece HTML'de uyarı gösterilecek, toast mesajı gösterilmeyecek
    }
  }

  setupMembershipEndDateCalculation() {
    const dayControl = this.membershipAddForm.get('day');
    const startDateControl = this.membershipAddForm.get('startDate');

    if (dayControl) {
      dayControl.valueChanges.subscribe(() => {
        this.calculateEndDate();
      });
    }

    if (startDateControl) {
      startDateControl.valueChanges.subscribe(() => {
        this.calculateEndDate();
      });
    }
  }

  private _filterMembers(value: string): Member[] {
    const filterValue = value.toLowerCase();
    return this.members.filter(member =>
      member.name.toLowerCase().includes(filterValue) ||
      member.phoneNumber.includes(filterValue)
    );
  }

  private _filterMembershipTypes(value: string): MembershipType[] {
    // Always return all membership types regardless of input
    return this.membershipTypes;
  }

  displayMembershipType(value: string): string {
    return value || '';
  }

  displayMember(member: Member): string {
    return member && member.name ? `${member.name} - ${member.phoneNumber}` : '';
  }

  calculateEndDate() {
    const startDateControl = this.membershipAddForm.get('startDate');
    const dayControl = this.membershipAddForm.get('day');

    if (startDateControl?.value && dayControl?.value) {
      const startDate = new Date(startDateControl.value);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + parseInt(dayControl.value));
      this.membershipAddForm.patchValue({ endDate: endDate });
    }
  }

  getLastMembershipInfo(memberId: number) {
    this.membershipService.getLastMembershipInfo(memberId).subscribe(
      response => {
        if (response.success) {
          const data = response.data;
          if (data.lastEndDate) {
            const endDate = new Date(data.lastEndDate);
            const now = new Date();
            if (endDate > now) {
              this.lastMembershipInfo = `Aktif üyelik mevcut. Bitiş: ${endDate.toLocaleDateString()}`;
              // Otomatik yenileme seçeneklerini gösterme - kullanıcı kafası karışmasın
              // this.checkForRenewalOptions(memberId);
            } else {
              this.lastMembershipInfo = `Son üyelik ${endDate.toLocaleDateString()} tarihinde sona erdi.`;
            }
          } else {
            this.lastMembershipInfo = "Daha önce üyelik kaydı bulunmuyor.";
          }
        }
      },
      (error: any) => {
        console.error('Error fetching last membership info:', error);
        this.lastMembershipInfo = null;
      }
    );
  }

  // Yenileme seçeneklerini kontrol et
  checkForRenewalOptions(memberId: number) {
    this.memberService.getMemberDeleteOptions(memberId).subscribe({
      next: (response) => {
        if (response.success && response.data.activeMemberships.length > 0) {
          this.renewalOptions = response.data.activeMemberships;
          this.showRenewalOptions = true;
        }
      },
      error: (error) => {
        console.error('Error fetching renewal options:', error);
      }
    });
  }

  // Yenileme seçeneği seç
  selectRenewalOption(membership: any) {
    this.isRenewalMode = true;
    this.existingMembershipId = membership.membershipID;
    this.showRenewalOptions = false;

    // Form'u yenileme moduna ayarla
    const selectedType = this.membershipTypes.find(type =>
      type.branch === membership.branch && type.typeName === membership.typeName
    );

    if (selectedType) {
      this.membershipAddForm.patchValue({
        membershipTypeID: `${selectedType.branch} - ${selectedType.typeName}`,
        day: selectedType.day,
        price: selectedType.price
      });
    }

    this.toastrService.info(`${membership.branch} - ${membership.typeName} üyeliği yenilenecek`, 'Yenileme Modu');
  }

  // Yeni üyelik modu
  selectNewMembership() {
    this.isRenewalMode = false;
    this.existingMembershipId = null;
    this.showRenewalOptions = false;
    this.toastrService.info('Yeni üyelik ekleme modu', 'Yeni Üyelik');
  }

  add() {
    if (!this.membershipAddForm.valid) {
      this.toastrService.error("Lütfen işaretli alanları doldurunuz", "Eksik Bilgi");

      // Tüm form kontrollerini dokunulmuş olarak işaretle
      Object.keys(this.membershipAddForm.controls).forEach(key => {
        const control = this.membershipAddForm.get(key);
        if (control) {
          control.markAsTouched();
          control.markAsDirty();
        }
      });

      // Eksik alanları vurgula ve titret
      this.highlightMissingFields();

      return;
    }

    this.isSubmitting = true;
    let membershipModel = Object.assign({}, this.membershipAddForm.value);

    // memberID işlemi
    if (typeof membershipModel.memberID === 'object') {
      membershipModel.memberID = membershipModel.memberID.memberID;
    }

    // PaymentStatus belirleme
    if (membershipModel.PaymentMethod === 'Borç') {
      membershipModel.paymentStatus = 'Pending';
    } else {
      membershipModel.paymentStatus = 'Completed';
    }

    // Membership type işlemleri
    const selectedType = this.membershipTypes.find(
      type => `${type.branch} - ${type.typeName}` === membershipModel.membershipTypeID
    );
    if (selectedType) {
      membershipModel.membershipTypeID = selectedType.membershipTypeID;
      // Kullanıcının girdiği day ve price değerleri korunacak, bu satırlar kaldırıldı.
      // membershipModel.day = selectedType.day;
      // membershipModel.price = selectedType.price;
    }

    // Tarih işlemleri
    if (membershipModel.startDate) {
      membershipModel.startDate = new Date(membershipModel.startDate);
    }
    if (membershipModel.endDate) {
      membershipModel.endDate = new Date(membershipModel.endDate);
    }

    // Akıllı yenileme için request oluştur
    const smartRequest: MembershipAddRequest = {
      ...membershipModel,
      isRenewal: this.isRenewalMode,
      existingMembershipID: this.existingMembershipId
    };

    this.membershipService.addSmart(smartRequest).subscribe(
      response => {
        this.toastrService.success(response.message, "Başarılı");
        this.resetForm();
        this.isSubmitting = false;
      },
      responseError => {
        if (responseError.error.Errors && responseError.error.Errors.length > 0) {
          responseError.error.Errors.forEach((error: { ErrorMessage: string | undefined; }) => {
            this.toastrService.error(error.ErrorMessage, "Doğrulama hatası");
          });
        } else {
          this.toastrService.error(responseError.error.message || "Bir hata oluştu", "Hata");
        }
        this.isSubmitting = false;
      }
    );
  }


  resetForm() {
    this.membershipAddForm.reset();

    // Başlangıç tarihini güncel tarih olarak ayarla
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0]; // YYYY-MM-DD formatı

    this.membershipAddForm.patchValue({
      startDate: formattedDate
    });

    this.lastMembershipInfo = null;

    // Akıllı yenileme durumunu sıfırla
    this.isRenewalMode = false;
    this.existingMembershipId = null;
    this.renewalOptions = [];
    this.showRenewalOptions = false;
  }

  // Eksik alanları vurgulama metodu
  highlightMissingFields() {
    // Zorunlu alanları kontrol et ve eksik olanları belirle
    const missingFields: string[] = [];
    const requiredControls: (HTMLElement | null)[] = [];

    // Üye kontrolü
    const memberIdControl = this.membershipAddForm.get('memberID');
    if (memberIdControl?.invalid) {
      missingFields.push('Üye');
      requiredControls.push(document.getElementById('memberID'));
    }

    // Üyelik Türü kontrolü
    const membershipTypeIdControl = this.membershipAddForm.get('membershipTypeID');
    if (membershipTypeIdControl?.invalid) {
      missingFields.push('Üyelik Türü');
      requiredControls.push(document.getElementById('membershipTypeID'));
    }

    // Başlangıç Tarihi kontrolü
    const startDateControl = this.membershipAddForm.get('startDate');
    if (startDateControl?.invalid) {
      missingFields.push('Başlangıç Tarihi');
      requiredControls.push(document.getElementById('startDate'));
    }

    // Gün Sayısı kontrolü
    const dayControl = this.membershipAddForm.get('day');
    if (dayControl?.invalid) {
      missingFields.push('Gün Sayısı');
      requiredControls.push(document.getElementById('day'));
    }

    // Ücret kontrolü
    const priceControl = this.membershipAddForm.get('price');
    if (priceControl?.invalid) {
      missingFields.push('Ücret');
      requiredControls.push(document.getElementById('price'));
    }

    // Ödeme Türü kontrolü
    const paymentMethodControl = this.membershipAddForm.get('PaymentMethod');
    if (paymentMethodControl?.invalid) {
      missingFields.push('Ödeme Türü');
      requiredControls.push(document.getElementById('PaymentMethod'));
    }

    // Eksik alan sayısına göre mesaj göster
    if (missingFields.length > 0) {
      const fieldList = missingFields.join(', ');
      this.toastrService.warning(`Lütfen şu alanları doldurun: ${fieldList}`, 'Eksik Alanlar');

      // Eksik alanları görsel olarak vurgula ve titret
      setTimeout(() => {
        requiredControls.forEach((element, index) => {
          if (element) {
            // Titreşim animasyonu ekle
            element.classList.add('shake-animation');

            // İlk eksik alana kaydır
            if (index === 0) {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });
              element.focus(); // İlk alana odaklan
            }

            // Animasyonu bir süre sonra kaldır
            setTimeout(() => {
              element.classList.remove('shake-animation');
            }, 600);
          }
        });
      }, 100);
    }
  }

  isFormValid(): boolean {
    const memberIdControl = this.membershipAddForm.get('memberID');
    const membershipTypeIdControl = this.membershipAddForm.get('membershipTypeID');
    const startDateControl = this.membershipAddForm.get('startDate');
    const dayControl = this.membershipAddForm.get('day'); // Eklendi
    const priceControl = this.membershipAddForm.get('price'); // Eklendi
    const paymentMethodControl = this.membershipAddForm.get('PaymentMethod');

    // Tüm zorunlu alanların geçerliliğini kontrol et
    return !!(memberIdControl?.valid &&
             membershipTypeIdControl?.valid &&
             startDateControl?.valid &&
             dayControl?.valid && // Eklendi
             priceControl?.valid && // Eklendi
             paymentMethodControl?.valid);
  }

  // Güncel tarihi ISO formatında döndüren metot
  getCurrentDate(): string {
    const today = new Date();
    return today.toISOString().split('T')[0]; // YYYY-MM-DD formatında
  }
}