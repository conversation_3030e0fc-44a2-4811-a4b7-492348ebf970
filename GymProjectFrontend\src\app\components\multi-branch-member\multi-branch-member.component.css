/* Ç<PERSON>lu Branş Üye Yönetimi <PERSON> */

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modern-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.member-details {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-weight: 600;
  color: var(--text-primary);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.memberships-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.membership-badge {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.membership-details {
  margin-left: 4px;
}

.remaining-days {
  font-weight: bold;
  margin-left: 4px;
}

/* Branş Badge Stilleri */
.badge-fitness {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.badge-crossfit {
  background: linear-gradient(135deg, #dc3545, #a71e2a);
  color: white;
}

.badge-pilates {
  background: linear-gradient(135deg, #e91e63, #ad1457);
  color: white;
}

.badge-yoga {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.badge-boxing {
  background: linear-gradient(135deg, #fd7e14, #e55a00);
  color: white;
}

.badge-default {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-buttons .btn {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  background: white;
  padding: 30px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Content Blur */
.content-blur {
  filter: blur(2px);
  pointer-events: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .member-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .modern-avatar {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
  
  .action-buttons .btn {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .memberships-container {
    gap: 4px;
  }
  
  .membership-badge .badge {
    font-size: 11px;
    padding: 4px 8px;
  }
}

/* Dark Mode Support */
[data-theme="dark"] .member-name {
  color: var(--text-primary-dark);
}

[data-theme="dark"] .loading-spinner {
  background: var(--card-bg-dark);
  color: var(--text-primary-dark);
}

[data-theme="dark"] .empty-state {
  color: var(--text-muted-dark);
}

/* Animation */
.zoom-in {
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Table Enhancements */
.modern-table td {
  vertical-align: middle;
  padding: 16px 12px;
}

.modern-table .badge {
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 20px;
  font-weight: 500;
}

/* Filter Section */
.form-group {
  margin-bottom: 0;
}

.form-label {
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 8px;
  display: block;
}

.modern-input,
.modern-select {
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 10px 12px;
  transition: all 0.2s ease;
}

.modern-input:focus,
.modern-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Pagination */
.pagination .page-link {
  border-radius: 6px;
  margin: 0 2px;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.pagination .page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
}

.pagination .page-link:hover {
  background-color: var(--hover-bg);
  border-color: var(--primary);
}
