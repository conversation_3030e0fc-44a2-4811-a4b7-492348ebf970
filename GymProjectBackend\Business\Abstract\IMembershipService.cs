﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;

namespace Business.Abstract
{
    public interface IMembershipService
    {
        IDataResult<List<Membership>> GetAll();
        IResult Add(MembershipAddDto membership);
        IResult Update(MembershipUpdateDto membership);
        IResult Delete(int id);
        IDataResult<List<Membership>> GetByMembershipId(int memberid);
        IDataResult<LastMembershipInfoDto> GetLastMembershipInfo(int memberId); 
        IResult FreezeMembership(MembershipFreezeRequestDto freezeRequest);
        IResult UnfreezeMembership(int membershipId);
        IDataResult<List<MembershipFreezeDto>> GetFrozenMemberships();
        IResult CancelFreeze(int membershipId);
        IResult ReactivateFromToday(int membershipId);
        IDataResult<MemberDeleteOptionsDto> GetMemberDeleteOptions(int memberId);
        IResult DeleteSpecificMembership(int membershipId, int memberId);
        IResult DeleteAllMemberships(int memberId);

    }
}