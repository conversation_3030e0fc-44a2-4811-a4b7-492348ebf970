using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Entities.DTOs
{
    /// <summary>
    /// Çoklu branş üyelik gösterimi için DTO
    /// </summary>
    public class MultiBranchMemberDto : IDto
    {
        public int MemberID { get; set; }
        public string Name { get; set; }
        public byte Gender { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public decimal Balance { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        
        /// <summary>
        /// Üyenin aktif üyelikleri
        /// </summary>
        public List<MembershipBranchDto> Memberships { get; set; } = new List<MembershipBranchDto>();
        
        /// <summary>
        /// Birleşik branş gösterimi (örn: "Fitness(40), Crossfit(60)")
        /// </summary>
        public string CombinedBranches => string.Join(", ", 
            Memberships.Where(m => m.IsActive && m.RemainingDays > 0)
                      .Select(m => $"{m.Branch}({m.RemainingDays})"));
        
        /// <summary>
        /// Toplam kalan gün sayısı
        /// </summary>
        public int TotalRemainingDays => Memberships
            .Where(m => m.IsActive && m.RemainingDays > 0)
            .Sum(m => m.RemainingDays);
        
        /// <summary>
        /// En erken bitiş tarihi
        /// </summary>
        public DateTime? EarliestEndDate => Memberships
            .Where(m => m.IsActive)
            .OrderBy(m => m.EndDate)
            .FirstOrDefault()?.EndDate;
        
        /// <summary>
        /// En geç bitiş tarihi
        /// </summary>
        public DateTime? LatestEndDate => Memberships
            .Where(m => m.IsActive)
            .OrderByDescending(m => m.EndDate)
            .FirstOrDefault()?.EndDate;
    }

    /// <summary>
    /// Üyelik branş detayları
    /// </summary>
    public class MembershipBranchDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsActive { get; set; }
        public bool IsFrozen { get; set; }
        public DateTime? FreezeStartDate { get; set; }
        public DateTime? FreezeEndDate { get; set; }
    }
}
