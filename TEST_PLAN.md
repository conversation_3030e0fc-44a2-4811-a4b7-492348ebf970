# 🧪 Çoklu Branş Üyelik Sistemi Test Planı

## 📋 Test Senaryoları

### 1. ✅ Akıllı Üyelik Yenileme Testi

#### Senaryo 1.1: Aynı Paket Yenileme
- **Test:** Üye fitness 1 aylık paketi var, aynı paketten 1 ay daha alıyor
- **Beklenen:** Mevcut üyelik EndDate'i uzar, yeni kayıt oluşmaz
- **Kontrol:** MembershipTypeID değişmez

#### Senaryo 1.2: Farklı Paket Yenileme  
- **Test:** Üye fitness 1 aylık paketi var, fitness 3 aylık paket alıyor
- **Beklenen:** Mevcut üyelik güncellenir, MembershipTypeID değişir
- **Kontrol:** Filtreleme panelinde doğru paket türünde görünür

#### Senaryo 1.3: Farklı Branş Ekleme
- **Test:** Üye fitness paketi var, crossfit paketi alıyor  
- **Beklenen:** Yeni üyelik ka<PERSON>, eski kayıt k<PERSON>
- **Kontrol:** İki ayrı aktif üyelik bulunur

### 2. 🔄 Çoklu Branş Gösterim Testi

#### Senaryo 2.1: Birleşik Branş Gösterimi
- **Test:** Üyenin Fitness(40) + Crossfit(60) üyeliği var
- **Beklenen:** "Fitness(40), Crossfit(60)" formatında gösterilir
- **Kontrol:** Toplam kalan gün 100 olarak hesaplanır

#### Senaryo 2.2: Branş Filtreleme
- **Test:** Fitness branşı filtresi seçilir
- **Beklenen:** Sadece fitness üyeliği olan üyeler listelenir
- **Kontrol:** Crossfit-only üyeler görünmez

### 3. 🛡️ Güvenli Silme Testi

#### Senaryo 3.1: Tek Üyelik Silme
- **Test:** Üyenin sadece 1 aktif üyeliği var, sil butonuna basılır
- **Beklenen:** Direkt silme onayı istenir
- **Kontrol:** Üye tamamen silinir

#### Senaryo 3.2: Çoklu Üyelik Silme
- **Test:** Üyenin 2+ aktif üyeliği var, sil butonuna basılır  
- **Beklenen:** Silme seçenekleri dialog'u açılır
- **Kontrol:** Hangi üyeliğin silineceği seçilebilir

#### Senaryo 3.3: Ödeme Uyarısı
- **Test:** Bekleyen ödemesi olan üyelik silinmeye çalışılır
- **Beklenen:** Ödeme uyarısı gösterilir
- **Kontrol:** Kullanıcı onayladıktan sonra silme işlemi gerçekleşir

### 4. 🔍 Performans Testi

#### Senaryo 4.1: Büyük Veri Seti
- **Test:** 10,000+ üye ile sayfalama testi
- **Beklenen:** Sayfa yükleme süresi < 2 saniye
- **Kontrol:** Memory usage normal seviyede

#### Senaryo 4.2: Arama Performansı
- **Test:** Üye adı ile arama (debounce ile)
- **Beklenen:** 300ms debounce sonrası hızlı sonuç
- **Kontrol:** Gereksiz API çağrısı yapılmaz

### 5. 🏢 Multi-Tenant Test

#### Senaryo 5.1: Şirket İzolasyonu
- **Test:** Farklı şirketlerden üyeler oluştur
- **Beklenen:** Her şirket sadece kendi üyelerini görür
- **Kontrol:** CompanyID filtreleri çalışır

#### Senaryo 5.2: Çapraz Şirket Erişim
- **Test:** Şirket A'dan Şirket B'nin üyesine erişim denemesi
- **Beklenen:** Erişim engellenir
- **Kontrol:** 403 Forbidden hatası alınır

## 🔧 Test Ortamı Kurulumu

### Backend Test
```bash
# 1. Migration çalıştır
sqlcmd -S server -d GymProject -i MultiBranchMembershipSystemMigration.sql

# 2. Test verisi oluştur
INSERT INTO Members (CompanyID, Name, Gender, PhoneNumber, Email, IsActive, CreationDate)
VALUES (1, 'TEST ÜYE 1', 1, '**********', '<EMAIL>', 1, GETDATE())

# 3. Çoklu üyelik oluştur
INSERT INTO Memberships (MemberID, MembershipTypeID, CompanyID, StartDate, EndDate, IsActive, CreationDate)
VALUES (1, 1, 1, GETDATE(), DATEADD(DAY, 30, GETDATE()), 1, GETDATE())
```

### Frontend Test
```bash
# 1. Yeni component'leri module'e ekle
ng generate component multi-branch-member
ng generate component member-delete-options-dialog

# 2. Routing güncelle
# app-routing.module.ts'e yeni route ekle

# 3. Test çalıştır
ng test
ng e2e
```

## ✅ Kabul Kriterleri

### Fonksiyonel Gereksinimler
- [ ] Üyelik yenileme MembershipTypeID'yi günceller
- [ ] Çoklu branş üyeliği desteklenir  
- [ ] Birleşik branş gösterimi çalışır
- [ ] Güvenli silme seçenekleri sunulur
- [ ] Ödeme uyarıları gösterilir

### Performans Gereksinimleri
- [ ] Sayfa yükleme < 2 saniye (10K+ kayıt)
- [ ] Arama debounce çalışır (300ms)
- [ ] Memory leak yok
- [ ] Database indexleri kullanılır

### Güvenlik Gereksinimleri  
- [ ] Multi-tenant izolasyon çalışır
- [ ] Authorization kontrolleri aktif
- [ ] SQL injection koruması var
- [ ] XSS koruması var

### Kullanılabilirlik Gereksinimleri
- [ ] Responsive tasarım çalışır
- [ ] Dark mode desteği var
- [ ] Loading state'leri gösterilir
- [ ] Error handling uygun

## 🐛 Bilinen Sorunlar ve Çözümler

### Sorun 1: Migration Timeout
**Çözüm:** Büyük veritabanlarında migration'ı parçalara böl

### Sorun 2: Frontend Build Hatası
**Çözüm:** Angular module'lerde import/export kontrolleri yap

### Sorun 3: Performance Degradation
**Çözüm:** Index kullanımını EXPLAIN PLAN ile kontrol et

## 📊 Test Raporu Şablonu

```markdown
# Test Raporu - [Tarih]

## Özet
- Toplam Test: X
- Başarılı: Y  
- Başarısız: Z
- Atlanan: W

## Detaylar
### ✅ Başarılı Testler
- Akıllı üyelik yenileme
- Çoklu branş gösterim
- ...

### ❌ Başarısız Testler  
- Test adı: Hata açıklaması
- Çözüm önerisi: ...

### ⚠️ Uyarılar
- Performance uyarıları
- Güvenlik notları
- ...
```

## 🚀 Deployment Checklist

- [ ] Migration dosyası test edildi
- [ ] Backend API testleri geçti
- [ ] Frontend build başarılı
- [ ] Database backup alındı
- [ ] Rollback planı hazır
- [ ] Monitoring kuruldu
- [ ] Documentation güncellendi
- [ ] Kullanıcı eğitimi planlandı
