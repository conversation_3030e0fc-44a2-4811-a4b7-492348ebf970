<div class="delete-options-dialog">
  <!-- Header -->
  <div class="dialog-header">
    <h4 class="dialog-title">
      <fa-icon [icon]="faExclamationTriangle" class="text-warning me-2"></fa-icon>
      Üyelik Silme Seçenekleri
    </h4>
    <p class="dialog-subtitle">
      <strong>{{ data.memberName }}</strong> adlı üyenin birden fazla aktif üyeliği bulunmaktadır.
      Hangi üyeliği silmek istediğinizi seçin.
    </p>
  </div>

  <!-- Membership Options -->
  <div class="dialog-body">
    <div class="membership-options">
      <div 
        *ngFor="let membership of data.activeMemberships" 
        class="membership-option"
        [class.selected]="selectedMembershipId === membership.membershipID"
        (click)="onSelectMembership(membership)"
      >
        <!-- Selection Radio -->
        <div class="selection-radio">
          <input 
            type="radio" 
            [value]="membership.membershipID"
            [(ngModel)]="selectedMembershipId"
            [id]="'membership-' + membership.membershipID"
          />
        </div>

        <!-- Membership Info -->
        <div class="membership-info">
          <!-- Branch Badge -->
          <div class="membership-header">
            <span [class]="'badge ' + getBranchBadgeClass(membership.branch)">
              {{ membership.branch }}
            </span>
            <span class="membership-type">{{ membership.typeName }}</span>
          </div>

          <!-- Details -->
          <div class="membership-details">
            <div class="detail-row">
              <span class="detail-label">
                <i class="fas fa-calendar-alt me-2"></i>Süre:
              </span>
              <span class="detail-value">
                {{ membership.startDate | date:'dd.MM.yyyy' }} - 
                {{ membership.endDate | date:'dd.MM.yyyy' }}
              </span>
            </div>

            <div class="detail-row">
              <span class="detail-label">
                <i class="fas fa-clock me-2"></i>Kalan Gün:
              </span>
              <span [class]="'detail-value ' + getRemainingDaysClass(membership.remainingDays)">
                {{ formatRemainingDays(membership.remainingDays) }}
              </span>
            </div>

            <div class="detail-row">
              <span class="detail-label">
                <fa-icon [icon]="faMoneyBillWave" class="me-2"></fa-icon>Toplam Ödeme:
              </span>
              <span class="detail-value">
                {{ membership.totalPaidAmount | currency:'TRY':'symbol':'1.2-2' }}
                <small class="text-muted">({{ membership.paymentCount }} ödeme)</small>
              </span>
            </div>

            <div class="detail-row">
              <span class="detail-label">
                <i class="fas fa-credit-card me-2"></i>Ödeme Durumu:
              </span>
              <span [class]="'detail-value ' + getPaymentStatusClass(membership.hasActivePayments)">
                {{ getPaymentStatusText(membership.hasActivePayments) }}
              </span>
            </div>
          </div>

          <!-- Warning for Active Payments -->
          <div *ngIf="membership.hasActivePayments" class="payment-warning">
            <fa-icon [icon]="faExclamationTriangle" class="text-warning me-2"></fa-icon>
            <span class="warning-text">Bu üyeliğin bekleyen ödemeleri bulunmaktadır!</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Info Box -->
    <div class="info-box">
      <fa-icon [icon]="faInfoCircle" class="text-info me-2"></fa-icon>
      <div class="info-content">
        <strong>Önemli:</strong> Seçilen üyelik kalıcı olarak silinecektir. 
        Bu işlem geri alınamaz ve üyeliğe ait tüm ödeme kayıtları da silinecektir.
      </div>
    </div>
  </div>

  <!-- Footer Actions -->
  <div class="dialog-footer">
    <button
      type="button"
      class="btn btn-secondary me-2"
      (click)="onCancel()"
      [disabled]="isDeleting"
    >
      İptal
    </button>
    <button
      type="button"
      class="btn btn-warning me-2"
      (click)="onDeleteAll()"
      [disabled]="isDeleting"
    >
      <fa-icon [icon]="faTrashAlt" class="me-2"></fa-icon>
      <span *ngIf="!isDeleting">Tümünü Sil</span>
      <span *ngIf="isDeleting">
        <i class="fas fa-spinner fa-spin me-2"></i>Siliniyor...
      </span>
    </button>
    <button
      type="button"
      class="btn btn-danger"
      (click)="onDeleteSelected()"
      [disabled]="!selectedMembershipId || isDeleting"
    >
      <fa-icon [icon]="faTrashAlt" class="me-2"></fa-icon>
      <span *ngIf="!isDeleting">Seçilen Üyeliği Sil</span>
      <span *ngIf="isDeleting">
        <i class="fas fa-spinner fa-spin me-2"></i>Siliniyor...
      </span>
    </button>
  </div>
</div>
